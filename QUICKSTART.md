# 🚀 دليل التشغيل السريع

## 📋 المتطلبات الأساسية

- **Python 3.8+** مثبت على النظام
- **pip** (مدير حزم Python)
- متصفح ويب حديث

## ⚡ التشغيل السريع (3 خطوات)

### 1️⃣ تحميل المشروع
```bash
# إذا كان لديك Git
git clone [repository-url]
cd car-center

# أو قم بتحميل الملفات وفك الضغط
```

### 2️⃣ تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3️⃣ تشغيل النظام
```bash
python test_app.py
```

**أو على Windows:**
```cmd
start.bat
```

**أو باستخدام ملف التشغيل المتقدم:**
```bash
python start.py
```

## 🌐 الوصول للنظام

1. افتح المتصفح
2. انتقل إلى: `http://localhost:5000`
3. استخدم بيانات الدخول:
   - **المستخدم**: `admin`
   - **كلمة المرور**: `admin123`

## 🎯 الخطوات الأولى

### 1. إضافة عميل جديد
- انقر على "إضافة عميل جديد" من لوحة التحكم
- أدخل البيانات الأساسية (الاسم ورقم الهاتف مطلوبان)
- احفظ البيانات

### 2. إضافة مركبة
- من صفحة العميل، انقر "إضافة مركبة"
- أدخل بيانات المركبة (الماركة، الموديل، رقم اللوحة)
- احفظ البيانات

### 3. إنشاء خدمة صيانة
- انقر "إضافة خدمة جديدة"
- اختر العميل والمركبة
- أدخل وصف العطل أو الخدمة المطلوبة
- حدد الأولوية والفني المسؤول

### 4. إدارة المخزون
- انتقل إلى قسم "المخزون"
- أضف قطع الغيار الأساسية
- حدد الحد الأدنى لكل قطعة

### 5. إنشاء فاتورة
- من صفحة الخدمة، انقر "إنشاء فاتورة"
- راجع التفاصيل والمبالغ
- احفظ الفاتورة

## 🔧 إعدادات مهمة

### تغيير كلمة مرور المدير
1. انقر على اسم المستخدم في الأعلى
2. اختر "تغيير كلمة المرور"
3. أدخل كلمة المرور الجديدة

### إضافة مستخدمين جدد
1. انتقل إلى "إدارة المستخدمين" (للمدير فقط)
2. انقر "إضافة مستخدم جديد"
3. حدد الصلاحيات المناسبة

### تخصيص الإعدادات
- عدّل ملف `config.py` للإعدادات المتقدمة
- أنشئ ملف `.env` للإعدادات الحساسة

## 📊 استخدام التقارير

### التقارير المالية
- انتقل إلى قسم "التقارير"
- اختر "التقارير المالية"
- حدد الفترة الزمنية المطلوبة

### تقارير الخدمات
- راجع أداء الفنيين
- تتبع أنواع الأعطال الشائعة
- مراقبة أوقات الإنجاز

### تقارير المخزون
- راقب المخزون المنخفض
- تتبع استهلاك قطع الغيار
- تحليل قيمة المخزون

## ⚠️ نصائح مهمة

### الأمان
- غيّر كلمة مرور المدير فوراً
- استخدم كلمات مرور قوية
- قم بعمل نسخ احتياطية دورية

### الأداء
- نظف قاعدة البيانات دورياً
- احذف الملفات غير المستخدمة
- راقب مساحة التخزين

### النسخ الاحتياطية
- انسخ مجلد `instance` دورياً
- احفظ نسخة من ملفات التكوين
- اختبر استعادة النسخ الاحتياطية

## 🆘 حل المشاكل الشائعة

### خطأ "Module not found"
```bash
pip install -r requirements.txt
```

### خطأ في قاعدة البيانات
```bash
python setup.py
```

### المنفذ 5000 مستخدم
- غيّر المنفذ في ملف التشغيل
- أو أوقف التطبيق الآخر

### مشاكل الترميز العربي
- تأكد من ترميز UTF-8
- استخدم متصفح حديث

## 📞 الحصول على المساعدة

### الوثائق
- اقرأ ملف `README.md` للتفاصيل الكاملة
- راجع ملف `FEATURES.md` للمميزات

### الدعم التقني
- أنشئ Issue في المستودع
- راجع الأخطاء في وحدة التحكم
- تحقق من ملفات السجل

### المجتمع
- شارك تجربتك مع المطورين الآخرين
- اقترح تحسينات جديدة
- ساهم في تطوير النظام

---

**نصيحة**: ابدأ بإدخال بيانات تجريبية لتتعرف على النظام قبل إدخال البيانات الحقيقية.

**تذكر**: قم بعمل نسخة احتياطية قبل أي تحديث أو تعديل مهم!
