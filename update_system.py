#!/usr/bin/env python3
"""
أداة تحديث وصيانة نظام إدارة مركز صيانة السيارات
"""

import os
import sys
import shutil
import sqlite3
from datetime import datetime
import argparse

def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    print("📦 جاري إنشاء نسخة احتياطية...")
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = "backups"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # نسخ قاعدة البيانات
    db_files = [
        "instance/test_car_center.db",
        "test_car_center.db",
        "car_center.db"
    ]
    
    backup_created = False
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    for db_file in db_files:
        if os.path.exists(db_file):
            backup_name = f"backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_name)
            shutil.copy2(db_file, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            backup_created = True
            break
    
    if not backup_created:
        print("⚠️ لم يتم العثور على قاعدة بيانات لنسخها")
    
    return backup_created

def check_database():
    """فحص سلامة قاعدة البيانات"""
    print("🔍 جاري فحص قاعدة البيانات...")
    
    db_files = [
        "instance/test_car_center.db",
        "test_car_center.db",
        "car_center.db"
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # فحص الجداول الأساسية
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                required_tables = ['user', 'customer', 'vehicle', 'service', 'part', 'invoice']
                existing_tables = [table[0] for table in tables]
                
                print(f"📊 الجداول الموجودة: {len(existing_tables)}")
                
                missing_tables = [table for table in required_tables if table not in existing_tables]
                if missing_tables:
                    print(f"⚠️ جداول مفقودة: {missing_tables}")
                else:
                    print("✅ جميع الجداول الأساسية موجودة")
                
                # فحص عدد السجلات
                for table in existing_tables:
                    if table in required_tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table};")
                        count = cursor.fetchone()[0]
                        print(f"   - {table}: {count} سجل")
                
                conn.close()
                return True
                
            except sqlite3.Error as e:
                print(f"❌ خطأ في قاعدة البيانات: {e}")
                return False
    
    print("❌ لم يتم العثور على قاعدة بيانات")
    return False

def update_database():
    """تحديث هيكل قاعدة البيانات"""
    print("🔄 جاري تحديث قاعدة البيانات...")
    
    try:
        # استيراد النماذج وإنشاء الجداول
        from app_complete import app, db
        
        with app.app_context():
            # إنشاء الجداول الجديدة أو المحدثة
            db.create_all()
            print("✅ تم تحديث هيكل قاعدة البيانات")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

def clean_logs():
    """تنظيف ملفات السجل القديمة"""
    print("🧹 جاري تنظيف ملفات السجل...")
    
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        print("📁 مجلد السجلات غير موجود")
        return
    
    # حذف ملفات السجل الأقدم من 30 يوم
    import time
    current_time = time.time()
    thirty_days = 30 * 24 * 60 * 60  # 30 يوم بالثواني
    
    cleaned_files = 0
    for filename in os.listdir(logs_dir):
        file_path = os.path.join(logs_dir, filename)
        if os.path.isfile(file_path):
            file_age = current_time - os.path.getmtime(file_path)
            if file_age > thirty_days:
                os.remove(file_path)
                cleaned_files += 1
    
    print(f"✅ تم حذف {cleaned_files} ملف سجل قديم")

def optimize_database():
    """تحسين أداء قاعدة البيانات"""
    print("⚡ جاري تحسين قاعدة البيانات...")
    
    db_files = [
        "instance/test_car_center.db",
        "test_car_center.db",
        "car_center.db"
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # تحسين قاعدة البيانات
                cursor.execute("VACUUM;")
                cursor.execute("ANALYZE;")
                
                conn.commit()
                conn.close()
                
                print(f"✅ تم تحسين قاعدة البيانات: {db_file}")
                return True
                
            except sqlite3.Error as e:
                print(f"❌ خطأ في تحسين قاعدة البيانات: {e}")
                return False
    
    print("❌ لم يتم العثور على قاعدة بيانات للتحسين")
    return False

def check_requirements():
    """فحص المتطلبات"""
    print("📋 جاري فحص المتطلبات...")
    
    required_packages = [
        'flask',
        'flask-sqlalchemy',
        'flask-login',
        'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ حزم مفقودة: {', '.join(missing_packages)}")
        print("💡 قم بتشغيل: pip install -r requirements.txt")
        return False
    else:
        print("✅ جميع المتطلبات متوفرة")
        return True

def show_system_info():
    """عرض معلومات النظام"""
    print("ℹ️ معلومات النظام:")
    print(f"   - Python: {sys.version}")
    print(f"   - نظام التشغيل: {os.name}")
    print(f"   - المجلد الحالي: {os.getcwd()}")
    
    # حجم قاعدة البيانات
    db_files = [
        "instance/test_car_center.db",
        "test_car_center.db",
        "car_center.db"
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            size_mb = size / (1024 * 1024)
            print(f"   - حجم قاعدة البيانات: {size_mb:.2f} MB")
            break

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description='أداة تحديث وصيانة النظام')
    parser.add_argument('--backup', action='store_true', help='إنشاء نسخة احتياطية')
    parser.add_argument('--check', action='store_true', help='فحص النظام')
    parser.add_argument('--update', action='store_true', help='تحديث قاعدة البيانات')
    parser.add_argument('--clean', action='store_true', help='تنظيف الملفات القديمة')
    parser.add_argument('--optimize', action='store_true', help='تحسين قاعدة البيانات')
    parser.add_argument('--all', action='store_true', help='تنفيذ جميع العمليات')
    parser.add_argument('--info', action='store_true', help='عرض معلومات النظام')
    
    args = parser.parse_args()
    
    print("🔧 أداة تحديث وصيانة نظام إدارة مركز صيانة السيارات")
    print("=" * 60)
    
    if args.info or not any(vars(args).values()):
        show_system_info()
        print()
    
    if args.all or args.backup:
        create_backup()
        print()
    
    if args.all or args.check:
        check_requirements()
        check_database()
        print()
    
    if args.all or args.update:
        update_database()
        print()
    
    if args.all or args.clean:
        clean_logs()
        print()
    
    if args.all or args.optimize:
        optimize_database()
        print()
    
    print("✅ تم الانتهاء من عمليات الصيانة")

if __name__ == '__main__':
    main()
