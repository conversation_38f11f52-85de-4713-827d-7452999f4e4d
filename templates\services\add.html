{% extends "base.html" %}

{% block title %}إضافة خدمة جديدة - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-tools"></i>
        إضافة خدمة جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('services.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للخدمات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">بيانات الخدمة</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="serviceForm">
                    <!-- اختيار العميل والمركبة -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}" 
                                        {% if request.args.get('customer_id') == customer.id|string %}selected{% endif %}>
                                    {{ customer.name }} - {{ customer.phone }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="vehicle_id" class="form-label">المركبة <span class="text-danger">*</span></label>
                            <select class="form-select" id="vehicle_id" name="vehicle_id" required>
                                <option value="">اختر المركبة</option>
                                <!-- سيتم تحديثها عبر JavaScript -->
                            </select>
                        </div>
                    </div>

                    <!-- وصف الخدمة -->
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف العطل أو الخدمة المطلوبة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="اكتب وصفاً تفصيلياً للعطل أو الخدمة المطلوبة..." required></textarea>
                    </div>

                    <!-- نوع الخدمة والأولوية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="service_type" class="form-label">نوع الخدمة</label>
                            <select class="form-select" id="service_type" name="service_type">
                                <option value="">اختر نوع الخدمة</option>
                                <option value="صيانة دورية">صيانة دورية</option>
                                <option value="إصلاح عطل">إصلاح عطل</option>
                                <option value="تغيير زيت">تغيير زيت</option>
                                <option value="فحص شامل">فحص شامل</option>
                                <option value="إصلاح فرامل">إصلاح فرامل</option>
                                <option value="إصلاح محرك">إصلاح محرك</option>
                                <option value="إصلاح كهرباء">إصلاح كهرباء</option>
                                <option value="إصلاح تكييف">إصلاح تكييف</option>
                                <option value="تغيير إطارات">تغيير إطارات</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                    </div>

                    <!-- الفني المسؤول -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="technician_name" class="form-label">الفني المسؤول</label>
                            <input type="text" class="form-control" id="technician_name" name="technician_name" 
                                   placeholder="اسم الفني المسؤول عن الخدمة">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="labor_cost" class="form-label">تكلفة العمالة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="labor_cost" name="labor_cost" 
                                       step="0.01" min="0" value="0">
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات إضافية -->
                    <div class="mb-3">
                        <label for="technician_notes" class="form-label">ملاحظات الفني</label>
                        <textarea class="form-control" id="technician_notes" name="technician_notes" rows="3" 
                                  placeholder="ملاحظات إضافية من الفني..."></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            حفظ الخدمة
                        </button>
                        <a href="{{ url_for('services.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-lg"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        سيتم إنشاء رقم خدمة تلقائياً
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        يمكن إضافة قطع الغيار لاحقاً
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        يمكن تحديث حالة الخدمة بعد الحفظ
                    </li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-flag"></i>
                    مستويات الأولوية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <span class="badge priority-low">منخفضة</span>
                    <small class="text-muted ms-2">خدمات عادية</small>
                </div>
                <div class="mb-2">
                    <span class="badge priority-medium">متوسطة</span>
                    <small class="text-muted ms-2">خدمات مهمة</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning text-dark">عالية</span>
                    <small class="text-muted ms-2">تحتاج اهتمام سريع</small>
                </div>
                <div class="mb-2">
                    <span class="badge priority-high">عاجلة</span>
                    <small class="text-muted ms-2">أولوية قصوى</small>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightbulb"></i>
                    نصائح
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="bi bi-arrow-left text-primary"></i>
                        كن دقيقاً في وصف العطل
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-arrow-left text-primary"></i>
                        حدد الأولوية حسب حالة العطل
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-arrow-left text-primary"></i>
                        يمكن تعديل التكلفة لاحقاً
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث قائمة المركبات عند اختيار العميل
document.getElementById('customer_id').addEventListener('change', function() {
    const customerId = this.value;
    const vehicleSelect = document.getElementById('vehicle_id');
    
    // مسح الخيارات الحالية
    vehicleSelect.innerHTML = '<option value="">اختر المركبة</option>';
    
    if (customerId) {
        // جلب مركبات العميل
        fetch(`/vehicles/api/by_customer/${customerId}`)
            .then(response => response.json())
            .then(vehicles => {
                vehicles.forEach(vehicle => {
                    const option = document.createElement('option');
                    option.value = vehicle.id;
                    option.textContent = vehicle.display_name;
                    vehicleSelect.appendChild(option);
                });
                
                // اختيار المركبة إذا كانت محددة في URL
                const urlParams = new URLSearchParams(window.location.search);
                const vehicleId = urlParams.get('vehicle_id');
                if (vehicleId) {
                    vehicleSelect.value = vehicleId;
                }
            })
            .catch(error => {
                console.error('خطأ في جلب المركبات:', error);
            });
    }
});

// تحديد العميل والمركبة من URL إذا كانا موجودين
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const customerId = urlParams.get('customer_id');
    
    if (customerId) {
        document.getElementById('customer_id').value = customerId;
        document.getElementById('customer_id').dispatchEvent(new Event('change'));
    }
});

// تحديث لون الأولوية
document.getElementById('priority').addEventListener('change', function() {
    const priority = this.value;
    const card = this.closest('.card');
    
    // إزالة الألوان السابقة
    card.classList.remove('border-success', 'border-warning', 'border-danger');
    
    // إضافة اللون المناسب
    switch(priority) {
        case 'low':
            card.classList.add('border-success');
            break;
        case 'medium':
            // لون افتراضي
            break;
        case 'high':
            card.classList.add('border-warning');
            break;
        case 'urgent':
            card.classList.add('border-danger');
            break;
    }
});
</script>
{% endblock %}
