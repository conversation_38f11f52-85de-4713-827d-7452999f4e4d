/**
 * ملف JavaScript الرئيسي لنظام إدارة مركز صيانة السيارات
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تهيئة التلميحات
    initializeTooltips();
    
    // تهيئة التنبيهات التلقائية
    initializeAlerts();
    
    // تهيئة البحث التلقائي
    initializeAutoSearch();
    
    // تهيئة التحديث التلقائي للوقت
    initializeTimeUpdates();
    
    // تهيئة الاختصارات
    initializeKeyboardShortcuts();
    
    console.log('✅ تم تهيئة التطبيق بنجاح');
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة التنبيهات التلقائية
 */
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            if (bsAlert) {
                bsAlert.close();
            }
        }, 5000);
    });
}

/**
 * تهيئة البحث التلقائي
 */
function initializeAutoSearch() {
    const searchInputs = document.querySelectorAll('.auto-search');
    searchInputs.forEach(function(input) {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                performAutoSearch(input);
            }, 500);
        });
    });
}

/**
 * تنفيذ البحث التلقائي
 */
function performAutoSearch(input) {
    const query = input.value.trim();
    const targetUrl = input.dataset.searchUrl;
    
    if (query.length >= 2 && targetUrl) {
        fetch(`${targetUrl}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data, input);
            })
            .catch(error => {
                console.error('خطأ في البحث:', error);
            });
    }
}

/**
 * عرض نتائج البحث
 */
function displaySearchResults(results, input) {
    const resultsContainer = document.getElementById(input.dataset.resultsContainer);
    if (!resultsContainer) return;
    
    resultsContainer.innerHTML = '';
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="text-muted p-2">لا توجد نتائج</div>';
        return;
    }
    
    results.forEach(function(item) {
        const resultItem = document.createElement('div');
        resultItem.className = 'search-result-item p-2 border-bottom';
        resultItem.innerHTML = `
            <div class="fw-bold">${item.name}</div>
            <small class="text-muted">${item.details || ''}</small>
        `;
        resultItem.addEventListener('click', function() {
            selectSearchResult(item, input);
        });
        resultsContainer.appendChild(resultItem);
    });
}

/**
 * اختيار نتيجة البحث
 */
function selectSearchResult(item, input) {
    input.value = item.name;
    if (input.dataset.valueField) {
        const valueField = document.getElementById(input.dataset.valueField);
        if (valueField) {
            valueField.value = item.id;
        }
    }
    
    const resultsContainer = document.getElementById(input.dataset.resultsContainer);
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
    
    // إطلاق حدث مخصص
    input.dispatchEvent(new CustomEvent('searchResultSelected', { detail: item }));
}

/**
 * تهيئة التحديث التلقائي للوقت
 */
function initializeTimeUpdates() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000); // كل دقيقة
}

/**
 * تحديث الوقت الحالي
 */
function updateCurrentTime() {
    const timeElements = document.querySelectorAll('.current-time');
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    timeElements.forEach(function(element) {
        element.textContent = timeString;
    });
}

/**
 * تهيئة اختصارات لوحة المفاتيح
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('button[type="submit"], .btn-save');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Ctrl + N لإضافة جديد
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const addButton = document.querySelector('.btn-add, [href*="/add"]');
            if (addButton) {
                addButton.click();
            }
        }
        
        // ESC لإلغاء أو إغلاق المودال
        if (e.key === 'Escape') {
            const modal = document.querySelector('.modal.show');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
        }
    });
}

/**
 * عرض رسالة تأكيد
 */
function showConfirmation(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * عرض رسالة نجاح
 */
function showSuccess(message) {
    showAlert(message, 'success');
}

/**
 * عرض رسالة خطأ
 */
function showError(message) {
    showAlert(message, 'danger');
}

/**
 * عرض رسالة تحذير
 */
function showWarning(message) {
    showAlert(message, 'warning');
}

/**
 * عرض رسالة معلومات
 */
function showInfo(message) {
    showAlert(message, 'info');
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || document.body;
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alertElement, alertContainer.firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        const bsAlert = new bootstrap.Alert(alertElement);
        if (bsAlert) {
            bsAlert.close();
        }
    }, 5000);
}

/**
 * تحميل البيانات عبر AJAX
 */
function loadData(url, callback, errorCallback) {
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (callback) callback(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            if (errorCallback) {
                errorCallback(error);
            } else {
                showError('حدث خطأ في تحميل البيانات');
            }
        });
}

/**
 * إرسال البيانات عبر AJAX
 */
function sendData(url, data, method = 'POST', callback, errorCallback) {
    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    };
    
    fetch(url, options)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (callback) callback(data);
        })
        .catch(error => {
            console.error('خطأ في إرسال البيانات:', error);
            if (errorCallback) {
                errorCallback(error);
            } else {
                showError('حدث خطأ في إرسال البيانات');
            }
        });
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number, decimals = 2) {
    return parseFloat(number).toFixed(decimals);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'ر.س') {
    return `${formatNumber(amount)} ${currency}`;
}

/**
 * تنسيق التاريخ
 */
function formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    switch (format) {
        case 'YYYY-MM-DD':
            return `${year}-${month}-${day}`;
        case 'DD/MM/YYYY':
            return `${day}/${month}/${year}`;
        case 'DD-MM-YYYY':
            return `${day}-${month}-${year}`;
        default:
            return d.toLocaleDateString('ar-SA');
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

/**
 * التحقق من صحة رقم الهاتف
 */
function validatePhone(phone) {
    const re = /^[\+]?[0-9\s\-\(\)]{10,}$/;
    return re.test(phone);
}

/**
 * تنظيف النص
 */
function sanitizeText(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * نسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showSuccess('تم نسخ النص إلى الحافظة');
    }).catch(function(err) {
        console.error('خطأ في النسخ:', err);
        showError('فشل في نسخ النص');
    });
}

/**
 * طباعة الصفحة
 */
function printPage() {
    window.print();
}

/**
 * تصدير البيانات
 */
function exportData(format, data, filename) {
    // يمكن تطوير هذه الوظيفة لاحقاً
    console.log(`تصدير البيانات بصيغة ${format}`);
    showInfo('ميزة التصدير ستكون متاحة قريباً');
}

// تصدير الوظائف للاستخدام العام
window.CarCenterApp = {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirmation,
    loadData,
    sendData,
    formatNumber,
    formatCurrency,
    formatDate,
    validateEmail,
    validatePhone,
    copyToClipboard,
    printPage,
    exportData
};
