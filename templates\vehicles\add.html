{% extends "base.html" %}

{% block title %}إضافة مركبة جديدة - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-car-front-fill"></i>
        إضافة مركبة جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('vehicles.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">بيانات المركبة</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="vehicleForm">
                    <!-- العميل -->
                    <div class="mb-3">
                        <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                        <select class="form-select" id="customer_id" name="customer_id" required>
                            <option value="">اختر العميل</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}" 
                                    {% if request.args.get('customer_id') == customer.id|string %}selected{% endif %}>
                                {{ customer.name }} - {{ customer.phone }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- بيانات المركبة الأساسية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="make" class="form-label">الماركة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="make" name="make" 
                                   placeholder="مثل: تويوتا، هوندا، نيسان" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="model" class="form-label">الموديل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="model" name="model" 
                                   placeholder="مثل: كامري، أكورد، التيما" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="year" class="form-label">سنة الصنع</label>
                            <input type="number" class="form-control" id="year" name="year" 
                                   min="1980" max="2030" placeholder="2020">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="color" class="form-label">اللون</label>
                            <input type="text" class="form-control" id="color" name="color" 
                                   placeholder="أبيض، أسود، فضي">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="plate_number" class="form-label">رقم اللوحة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="plate_number" name="plate_number" 
                                   placeholder="أ ب ج 123" required>
                        </div>
                    </div>

                    <!-- معلومات تقنية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="chassis_number" class="form-label">رقم الهيكل</label>
                            <input type="text" class="form-control" id="chassis_number" name="chassis_number" 
                                   placeholder="17 رقم/حرف">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="engine_number" class="form-label">رقم المحرك</label>
                            <input type="text" class="form-control" id="engine_number" name="engine_number">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="mileage" class="form-label">عداد الكيلومترات</label>
                            <input type="number" class="form-control" id="mileage" name="mileage" 
                                   min="0" placeholder="100000">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="fuel_type" class="form-label">نوع الوقود</label>
                            <select class="form-select" id="fuel_type" name="fuel_type">
                                <option value="">اختر نوع الوقود</option>
                                <option value="بنزين">بنزين</option>
                                <option value="ديزل">ديزل</option>
                                <option value="هجين">هجين</option>
                                <option value="كهربائي">كهربائي</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="transmission" class="form-label">ناقل الحركة</label>
                            <select class="form-select" id="transmission" name="transmission">
                                <option value="">اختر ناقل الحركة</option>
                                <option value="أوتوماتيك">أوتوماتيك</option>
                                <option value="عادي">عادي</option>
                                <option value="CVT">CVT</option>
                            </select>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي معلومات إضافية عن المركبة..."></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            حفظ المركبة
                        </button>
                        <a href="{{ url_for('vehicles.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-lg"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        رقم اللوحة يجب أن يكون فريد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        يمكن إضافة خدمات للمركبة بعد الحفظ
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        رقم الهيكل مفيد للتعرف على المركبة
                    </li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightbulb"></i>
                    نصائح
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="bi bi-arrow-left text-primary"></i>
                        تأكد من صحة رقم اللوحة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-arrow-left text-primary"></i>
                        رقم الهيكل موجود على وثائق المركبة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-arrow-left text-primary"></i>
                        يمكن تحديث عداد الكيلومترات لاحقاً
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحسين تجربة المستخدم
document.getElementById('vehicleForm').addEventListener('submit', function(e) {
    const plateNumber = document.getElementById('plate_number').value.trim();
    if (plateNumber.length < 3) {
        e.preventDefault();
        alert('رقم اللوحة قصير جداً');
        return false;
    }
});

// تنسيق رقم اللوحة تلقائياً
document.getElementById('plate_number').addEventListener('input', function(e) {
    let value = e.target.value.toUpperCase();
    e.target.value = value;
});
</script>
{% endblock %}
