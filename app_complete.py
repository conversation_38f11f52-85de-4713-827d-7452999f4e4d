#!/usr/bin/env python3
"""
التطبيق الكامل لنظام إدارة مركز صيانة السيارات
"""

import os
import logging
from datetime import datetime
from flask import Flask, render_template, redirect, url_for, flash, request
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_required, current_user
from werkzeug.security import generate_password_hash
from config import Config

# إنشاء التطبيق
app = Flask(__name__)
app.config.from_object(Config)

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'
login_manager.login_message_category = 'info'

# استيراد النماذج
from models import User, Customer, Vehicle, Service, Part, Invoice, InventoryMovement, ServicePart

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def index():
    """الصفحة الرئيسية مع لوحة التحكم"""
    from datetime import date, timedelta
    
    # إحصائيات سريعة
    today = date.today()
    this_week = today - timedelta(days=7)
    this_month = today.replace(day=1)
    
    # إحصائيات الخدمات
    total_services = Service.query.count()
    pending_services = Service.query.filter_by(status='pending').count()
    in_progress_services = Service.query.filter_by(status='in_progress').count()
    completed_services = Service.query.filter_by(status='completed').count()
    
    # خدمات اليوم
    today_services = Service.query.filter(
        Service.requested_date >= today
    ).count()
    
    # إحصائيات العملاء والمركبات
    total_customers = Customer.query.count()
    total_vehicles = Vehicle.query.count()
    
    # إحصائيات المخزون
    total_parts = Part.query.filter_by(is_active=True).count()
    low_stock_parts = Part.query.filter(
        Part.quantity_in_stock <= Part.minimum_stock,
        Part.is_active == True
    ).count()
    
    # إحصائيات مالية
    this_month_services = Service.query.filter(
        Service.requested_date >= this_month,
        Service.status == 'completed'
    ).all()
    this_month_revenue = sum(service.total_cost for service in this_month_services)
    
    # الخدمات الحديثة
    recent_services = Service.query.order_by(
        Service.requested_date.desc()
    ).limit(5).all()
    
    # تنبيهات المخزون
    low_stock_alerts = Part.query.filter(
        Part.quantity_in_stock <= Part.minimum_stock,
        Part.is_active == True
    ).limit(5).all()
    
    return render_template('index.html',
                         total_services=total_services,
                         pending_services=pending_services,
                         in_progress_services=in_progress_services,
                         completed_services=completed_services,
                         today_services=today_services,
                         total_customers=total_customers,
                         total_vehicles=total_vehicles,
                         total_parts=total_parts,
                         low_stock_parts=low_stock_parts,
                         this_month_revenue=this_month_revenue,
                         recent_services=recent_services,
                         low_stock_alerts=low_stock_alerts)

# تسجيل المسارات
def register_blueprints():
    """تسجيل جميع المسارات"""
    from routes.auth import auth
    from routes.customers import customers
    from routes.vehicles import vehicles
    from routes.services import services
    from routes.inventory import inventory
    from routes.reports import reports
    from routes.api import api
    
    app.register_blueprint(auth)
    app.register_blueprint(customers)
    app.register_blueprint(vehicles)
    app.register_blueprint(services)
    app.register_blueprint(inventory)
    app.register_blueprint(reports)
    app.register_blueprint(api)

# معالج الأخطاء
@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('errors/500.html'), 500

@app.errorhandler(403)
def forbidden_error(error):
    return render_template('errors/403.html'), 403

# فلاتر القوالب
@app.template_filter('currency')
def currency_filter(amount):
    """تنسيق العملة"""
    return f"{amount:,.2f} ر.س"

@app.template_filter('date_format')
def date_format_filter(date, format='%Y-%m-%d'):
    """تنسيق التاريخ"""
    if date:
        return date.strftime(format)
    return ''

@app.template_filter('status_badge')
def status_badge_filter(status):
    """تنسيق شارة الحالة"""
    badges = {
        'pending': 'bg-warning text-dark',
        'in_progress': 'bg-info',
        'completed': 'bg-success',
        'cancelled': 'bg-danger',
        'paid': 'bg-success',
        'partial': 'bg-warning',
        'unpaid': 'bg-danger'
    }
    return badges.get(status, 'bg-secondary')

@app.template_filter('status_text')
def status_text_filter(status):
    """نص الحالة بالعربية"""
    texts = {
        'pending': 'معلق',
        'in_progress': 'قيد التنفيذ',
        'completed': 'مكتمل',
        'cancelled': 'ملغي',
        'paid': 'مدفوع',
        'partial': 'مدفوع جزئياً',
        'unpaid': 'غير مدفوع'
    }
    return texts.get(status, status)

# متغيرات السياق العامة
@app.context_processor
def inject_globals():
    """حقن متغيرات عامة في جميع القوالب"""
    return {
        'current_year': datetime.now().year,
        'app_name': 'نظام إدارة مركز صيانة السيارات',
        'app_version': '1.0.0'
    }

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    try:
        # إنشاء عميل تجريبي
        if not Customer.query.first():
            customer = Customer(
                name='أحمد محمد',
                phone='0501234567',
                email='<EMAIL>',
                address='الرياض، المملكة العربية السعودية'
            )
            db.session.add(customer)
            db.session.commit()
            
            # إنشاء مركبة تجريبية
            vehicle = Vehicle(
                customer_id=customer.id,
                make='تويوتا',
                model='كامري',
                year=2020,
                color='أبيض',
                plate_number='أ ب ج 123',
                chassis_number='JT123456789',
                engine_number='ENG123456',
                mileage=50000,
                fuel_type='بنزين',
                transmission='أوتوماتيك'
            )
            db.session.add(vehicle)
            db.session.commit()
            
            # إنشاء قطع غيار تجريبية
            parts_data = [
                {'name': 'زيت المحرك', 'part_number': 'OIL001', 'category': 'زيوت', 'unit': 'لتر', 'cost_price': 25.0, 'selling_price': 35.0, 'quantity_in_stock': 50, 'minimum_stock': 10},
                {'name': 'فلتر الهواء', 'part_number': 'AIR001', 'category': 'فلاتر', 'unit': 'قطعة', 'cost_price': 15.0, 'selling_price': 25.0, 'quantity_in_stock': 20, 'minimum_stock': 5},
                {'name': 'تيل الفرامل', 'part_number': 'BRK001', 'category': 'فرامل', 'unit': 'طقم', 'cost_price': 80.0, 'selling_price': 120.0, 'quantity_in_stock': 8, 'minimum_stock': 3},
            ]
            
            for part_data in parts_data:
                part = Part(**part_data)
                db.session.add(part)
            
            db.session.commit()
            print("✅ تم إنشاء البيانات التجريبية")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        db.session.rollback()

def init_database():
    """تهيئة قاعدة البيانات"""
    try:
        # إنشاء الجداول
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # إنشاء المستخدم الافتراضي
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                full_name='مدير النظام',
                role='admin',
                email='<EMAIL>'
            )
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
        
        # إنشاء بيانات تجريبية
        create_sample_data()
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        db.session.rollback()

def setup_logging():
    """إعداد نظام السجلات"""
    if not app.debug:
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = logging.FileHandler(f'logs/app_{datetime.now().strftime("%Y%m%d")}.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('تم بدء تشغيل نظام إدارة مركز صيانة السيارات')

if __name__ == '__main__':
    # تسجيل المسارات
    register_blueprints()
    
    # إعداد السجلات
    setup_logging()
    
    # تهيئة قاعدة البيانات
    with app.app_context():
        init_database()
    
    # معلومات التشغيل
    print("\n" + "=" * 60)
    print("🚗 نظام إدارة مركز صيانة السيارات")
    print("=" * 60)
    print("🌐 الرابط: http://localhost:5000")
    print("👤 المستخدم الافتراضي: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 60)
    print("💡 اضغط Ctrl+C لإيقاف النظام")
    print("=" * 60)
    
    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5000)
