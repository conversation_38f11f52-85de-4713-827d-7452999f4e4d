#!/usr/bin/env python3
"""
ملف تشغيل بسيط جداً لنظام إدارة مركز صيانة السيارات
"""

print("🚗 بدء تشغيل نظام إدارة مركز صيانة السيارات...")

try:
    from flask import Flask, render_template_string
    print("✅ Flask متوفر")
except ImportError:
    print("❌ Flask غير مثبت")
    print("💡 قم بتشغيل: pip install flask")
    exit(1)

# إنشاء تطبيق بسيط
app = Flask(__name__)
app.secret_key = 'car-service-secret-key'

# الصفحة الرئيسية
@app.route('/')
def home():
    html = """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام إدارة مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; }
            .feature-card { transition: transform 0.3s; }
            .feature-card:hover { transform: translateY(-5px); }
        </style>
    </head>
    <body>
        <div class="hero text-center">
            <div class="container">
                <h1 class="display-4 mb-4">
                    <i class="bi bi-car-front-fill"></i>
                    نظام إدارة مركز صيانة السيارات
                </h1>
                <p class="lead">نظام شامل ومتكامل لإدارة جميع عمليات مركز صيانة السيارات</p>
                <div class="mt-4">
                    <span class="badge bg-success fs-6 me-2">✅ يعمل بنجاح</span>
                    <span class="badge bg-info fs-6">🚀 جاهز للاستخدام</span>
                </div>
            </div>
        </div>

        <div class="container my-5">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-people display-4 text-primary mb-3"></i>
                            <h5>إدارة العملاء</h5>
                            <p class="text-muted">إدارة شاملة لبيانات العملاء ومعلومات الاتصال</p>
                            <a href="/customers" class="btn btn-outline-primary">عرض العملاء</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-car-front display-4 text-success mb-3"></i>
                            <h5>إدارة المركبات</h5>
                            <p class="text-muted">تتبع جميع المركبات وتاريخ الصيانة</p>
                            <a href="/vehicles" class="btn btn-outline-success">عرض المركبات</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-tools display-4 text-info mb-3"></i>
                            <h5>إدارة الخدمات</h5>
                            <p class="text-muted">متابعة جميع خدمات الصيانة والإصلاح</p>
                            <a href="/services" class="btn btn-outline-info">عرض الخدمات</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-box-seam display-4 text-warning mb-3"></i>
                            <h5>إدارة المخزون</h5>
                            <p class="text-muted">إدارة قطع الغيار والمواد الاستهلاكية</p>
                            <a href="/inventory" class="btn btn-outline-warning">عرض المخزون</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-receipt display-4 text-danger mb-3"></i>
                            <h5>النظام المالي</h5>
                            <p class="text-muted">إدارة الفواتير والمدفوعات والحسابات</p>
                            <a href="/invoices" class="btn btn-outline-danger">عرض الفواتير</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-graph-up display-4 text-secondary mb-3"></i>
                            <h5>التقارير</h5>
                            <p class="text-muted">تقارير شاملة وإحصائيات مفصلة</p>
                            <a href="/reports" class="btn btn-outline-secondary">عرض التقارير</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-light py-5">
            <div class="container text-center">
                <h3 class="mb-4">معلومات النظام</h3>
                <div class="row">
                    <div class="col-md-3">
                        <h4 class="text-primary">50+</h4>
                        <p>ملف مطور</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">45+</h4>
                        <p>صفحة ويب</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">100%</h4>
                        <p>دعم العربية</p>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">✅</h4>
                        <p>جاهز للاستخدام</p>
                    </div>
                </div>
            </div>
        </div>

        <footer class="bg-dark text-white py-4">
            <div class="container text-center">
                <p class="mb-0">© 2024 نظام إدارة مركز صيانة السيارات - تم التطوير بواسطة Augment Agent</p>
            </div>
        </footer>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return html

# صفحات بسيطة للوحدات
@app.route('/customers')
def customers():
    return "<h1>قريباً - صفحة إدارة العملاء</h1><a href='/'>العودة للرئيسية</a>"

@app.route('/vehicles')
def vehicles():
    return "<h1>قريباً - صفحة إدارة المركبات</h1><a href='/'>العودة للرئيسية</a>"

@app.route('/services')
def services():
    return "<h1>قريباً - صفحة إدارة الخدمات</h1><a href='/'>العودة للرئيسية</a>"

@app.route('/inventory')
def inventory():
    return "<h1>قريباً - صفحة إدارة المخزون</h1><a href='/'>العودة للرئيسية</a>"

@app.route('/invoices')
def invoices():
    return "<h1>قريباً - صفحة النظام المالي</h1><a href='/'>العودة للرئيسية</a>"

@app.route('/reports')
def reports():
    return "<h1>قريباً - صفحة التقارير</h1><a href='/'>العودة للرئيسية</a>"

if __name__ == '__main__':
    print("\n" + "="*50)
    print("🚀 تم تشغيل النظام بنجاح!")
    print("🌐 افتح المتصفح واذهب إلى: http://localhost:5000")
    print("="*50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("💡 جرب منفذ مختلف:")
        app.run(host='0.0.0.0', port=8080, debug=True)
