#!/usr/bin/env python3
"""
ملف تشغيل بسيط لنظام إدارة مركز صيانة السيارات
"""

import sys
import webbrowser
import time
import threading

def check_requirements():
    """فحص المتطلبات الأساسية"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ متطلب مفقود: {e}")
        print("💡 قم بتشغيل: pip install -r requirements.txt")
        return False

def open_browser_delayed(url, delay=3):
    """فتح المتصفح بعد تأخير"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"🌐 تم فتح المتصفح: {url}")
        except Exception as e:
            print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")
    
    thread = threading.Thread(target=open_browser, daemon=True)
    thread.start()

def main():
    """الدالة الرئيسية"""
    print("🚗 نظام إدارة مركز صيانة السيارات")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    try:
        # استيراد التطبيق
        print("📦 جاري تحميل التطبيق...")
        from test_app import app
        print("✅ تم تحميل التطبيق بنجاح")
        
        # معلومات التشغيل
        url = "http://localhost:5000"
        print("\n" + "=" * 50)
        print("🚀 تم تشغيل النظام بنجاح!")
        print(f"🌐 الرابط: {url}")
        print("👤 المستخدم الافتراضي: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 50)
        print("💡 اضغط Ctrl+C لإيقاف النظام")
        print("=" * 50)
        
        # فتح المتصفح تلقائياً
        open_browser_delayed(url)
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        sys.exit(1)

if __name__ == '__main__':
    main()
