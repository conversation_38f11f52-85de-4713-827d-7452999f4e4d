@echo off
chcp 65001 >nul
title نظام إدارة مركز صيانة السيارات

echo.
echo 🚗 نظام إدارة مركز صيانة السيارات
echo ================================================
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo 📦 فحص المتطلبات...
python -c "import flask, flask_sqlalchemy, flask_login" >nul 2>&1
if errorlevel 1 (
    echo 📦 جاري تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ خطأ في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة
echo.

REM تشغيل النظام
echo 🚀 جاري تشغيل النظام...
echo.
echo ================================================
echo 🌐 الرابط: http://localhost:5000
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo ================================================
echo 💡 اضغط Ctrl+C لإيقاف النظام
echo.

python start.py

pause
