{% extends "base.html" %}

{% block title %}إدارة المخزون - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-box-seam"></i>
        إدارة المخزون
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('inventory.add') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                إضافة قطعة غيار
            </a>
            <a href="{{ url_for('inventory.low_stock') }}" class="btn btn-warning">
                <i class="bi bi-exclamation-triangle"></i>
                تنبيهات المخزون
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-3">
    <div class="col-md-4">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="البحث بالاسم أو رقم القطعة..." 
                   value="{{ search }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-danger ms-2">
                <i class="bi bi-x"></i>
            </a>
            {% endif %}
        </form>
    </div>
    <div class="col-md-3">
        <form method="GET">
            {% if search %}<input type="hidden" name="search" value="{{ search }}">{% endif %}
            <select class="form-select" name="category" onchange="this.form.submit()">
                <option value="">جميع الفئات</option>
                {% for cat in categories %}
                <option value="{{ cat }}" {% if selected_category == cat %}selected{% endif %}>{{ cat }}</option>
                {% endfor %}
            </select>
        </form>
    </div>
    <div class="col-md-2">
        <form method="GET">
            {% if search %}<input type="hidden" name="search" value="{{ search }}">{% endif %}
            {% if selected_category %}<input type="hidden" name="category" value="{{ selected_category }}">{% endif %}
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="low_stock" value="true" 
                       {% if low_stock %}checked{% endif %} onchange="this.form.submit()">
                <label class="form-check-label">
                    مخزون منخفض فقط
                </label>
            </div>
        </form>
    </div>
</div>

<!-- Parts Table -->
<div class="card">
    <div class="card-body">
        {% if parts.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>القطعة</th>
                        <th>الفئة</th>
                        <th>الماركة</th>
                        <th>المخزون</th>
                        <th>الحد الأدنى</th>
                        <th>سعر التكلفة</th>
                        <th>سعر البيع</th>
                        <th>المورد</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for part in parts.items %}
                    <tr {% if part.is_low_stock %}class="table-warning"{% endif %}>
                        <td>
                            <div>
                                <strong>{{ part.name }}</strong>
                                <br><small class="text-muted">{{ part.part_number }}</small>
                                {% if part.description %}
                                <br><small class="text-muted">{{ part.description[:30] }}{% if part.description|length > 30 %}...{% endif %}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ part.category or '-' }}</td>
                        <td>{{ part.brand or '-' }}</td>
                        <td>
                            <span class="badge {% if part.is_low_stock %}bg-danger{% else %}bg-success{% endif %}">
                                {{ part.quantity_in_stock }} {{ part.unit }}
                            </span>
                            {% if part.is_low_stock %}
                            <br><small class="text-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                                مخزون منخفض
                            </small>
                            {% endif %}
                        </td>
                        <td>{{ part.minimum_stock }} {{ part.unit }}</td>
                        <td>{{ "%.2f"|format(part.cost_price) }} ر.س</td>
                        <td>{{ "%.2f"|format(part.selling_price) }} ر.س</td>
                        <td>
                            {% if part.supplier_name %}
                                {{ part.supplier_name }}
                                {% if part.supplier_contact %}
                                <br><small class="text-muted">{{ part.supplier_contact }}</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if part.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('inventory.view', id=part.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('inventory.edit', id=part.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="showStockModal({{ part.id }}, '{{ part.name }}')" title="تعديل المخزون">
                                    <i class="bi bi-arrow-up-down"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if parts.pages > 1 %}
        <nav aria-label="صفحات قطع الغيار">
            <ul class="pagination justify-content-center">
                {% if parts.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('inventory.index', page=parts.prev_num, search=search, category=selected_category, low_stock=low_stock) }}">السابق</a>
                </li>
                {% endif %}

                {% for page_num in parts.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != parts.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('inventory.index', page=page_num, search=search, category=selected_category, low_stock=low_stock) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if parts.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('inventory.index', page=parts.next_num, search=search, category=selected_category, low_stock=low_stock) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-box-seam display-1 text-muted"></i>
            <h4 class="mt-3">لا توجد قطع غيار</h4>
            {% if search or selected_category or low_stock %}
            <p class="text-muted">لم يتم العثور على قطع غيار مطابقة للفلاتر المحددة</p>
            <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">عرض جميع قطع الغيار</a>
            {% else %}
            <p class="text-muted">ابدأ بإضافة قطع غيار جديدة</p>
            <a href="{{ url_for('inventory.add') }}" class="btn btn-primary">إضافة قطعة غيار</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="stockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="stockForm" method="POST">
                <div class="modal-body">
                    <p>تعديل مخزون: <strong id="partName"></strong></p>
                    
                    <div class="mb-3">
                        <label for="movement_type" class="form-label">نوع الحركة</label>
                        <select class="form-select" id="movement_type" name="movement_type" required>
                            <option value="in">إضافة للمخزون</option>
                            <option value="out">خصم من المخزون</option>
                            <option value="adjustment">تعديل الكمية</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">الكمية</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reference_number" class="form-label">رقم مرجعي</label>
                        <input type="text" class="form-control" id="reference_number" name="reference_number" 
                               placeholder="رقم الفاتورة أو المرجع">
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showStockModal(partId, partName) {
    document.getElementById('partName').textContent = partName;
    document.getElementById('stockForm').action = '/inventory/' + partId + '/adjust_stock';
    new bootstrap.Modal(document.getElementById('stockModal')).show();
}
</script>
{% endblock %}
