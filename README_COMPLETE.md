# 🚗 نظام إدارة مركز صيانة السيارات

نظام شامل ومتكامل لإدارة مراكز صيانة السيارات مطور بـ Python و Flask مع واجهة عربية احترافية

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0+-green.svg)](https://flask.palletsprojects.com)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3-purple.svg)](https://getbootstrap.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 نظرة عامة

نظام إدارة مركز صيانة السيارات هو حل متكامل يوفر جميع الأدوات اللازمة لإدارة مركز صيانة حديث بكفاءة عالية. النظام مصمم خصيصاً للسوق العربي مع دعم كامل للغة العربية وتصميم متجاوب يعمل على جميع الأجهزة.

## ✨ المميزات الرئيسية

### 👥 إدارة العملاء
- إضافة وتعديل وحذف بيانات العملاء
- البحث المتقدم والفلترة
- تتبع تاريخ العميل الكامل
- إدارة معلومات الاتصال والعناوين

### 🚙 إدارة المركبات
- تسجيل مركبات جديدة مع جميع التفاصيل
- ربط المركبات بالعملاء
- تتبع تاريخ الصيانة لكل مركبة
- إدارة معلومات المركبة (الماركة، الموديل، رقم اللوحة، إلخ)

### 🔧 إدارة الخدمات والصيانة
- إنشاء طلبات خدمة جديدة
- تتبع حالة الخدمة (معلق، قيد التنفيذ، مكتمل)
- إدارة الأولويات (منخفض، متوسط، عالي، عاجل)
- تخصيص الفنيين للخدمات
- حساب تكاليف العمالة وقطع الغيار

### 📦 إدارة المخزون
- إضافة وإدارة قطع الغيار
- تتبع الكميات والأسعار
- تنبيهات المخزون المنخفض
- تتبع حركة المخزون (دخول، خروج، تعديل)
- إدارة معلومات الموردين

### 💰 النظام المالي
- إنشاء الفواتير التلقائية
- تتبع المدفوعات والمبالغ المستحقة
- طرق دفع متعددة
- حساب الضرائب والخصومات
- تقارير مالية شاملة

### 📊 التقارير والإحصائيات
- تقارير مالية مع رسوم بيانية
- تقارير الخدمات وأداء الفنيين
- تقارير المخزون والتنبيهات
- تقارير العملاء والنشاط
- لوحة تحكم شاملة مع إحصائيات

### 🎨 واجهة المستخدم
- تصميم عربي متجاوب (RTL)
- استخدام Bootstrap 5 مع تخصيصات
- أيقونات وألوان متناسقة
- رسائل تنبيه وتأكيد
- نظام تنقل سهل وبديهي

## 🚀 التثبيت السريع

### الطريقة الأسهل (موصى بها)
```bash
python install.py
```

### التثبيت اليدوي
1. **تأكد من وجود Python 3.8+**
   ```bash
   python --version
   ```

2. **ثبت المتطلبات**
   ```bash
   pip install -r requirements.txt
   ```

3. **شغل النظام**
   ```bash
   python run_final.py
   ```

## 🌐 الوصول للنظام

بعد التشغيل، افتح المتصفح وانتقل إلى: **http://localhost:5000**

### بيانات الدخول الافتراضية
- **المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 📁 هيكل المشروع

```
car_center/
├── 📄 app_complete.py        # التطبيق الكامل
├── 📄 test_app.py           # التطبيق البسيط للاختبار
├── 📄 run_final.py          # ملف التشغيل النهائي
├── 📄 install.py            # أداة التثبيت التلقائي
├── 📄 update_system.py      # أداة التحديث والصيانة
├── 📄 config.py             # إعدادات التطبيق
├── 📄 requirements.txt      # المتطلبات
├── 📁 models/               # نماذج قاعدة البيانات
│   ├── 📄 __init__.py
│   ├── 📄 customer.py       # نموذج العملاء
│   ├── 📄 vehicle.py        # نموذج المركبات
│   ├── 📄 service.py        # نموذج الخدمات
│   ├── 📄 inventory.py      # نموذج المخزون
│   └── 📄 invoice.py        # نموذج الفواتير
├── 📁 routes/               # مسارات التطبيق
│   ├── 📄 __init__.py
│   ├── 📄 auth.py           # المصادقة
│   ├── 📄 customers.py      # إدارة العملاء
│   ├── 📄 vehicles.py       # إدارة المركبات
│   ├── 📄 services.py       # إدارة الخدمات
│   ├── 📄 inventory.py      # إدارة المخزون
│   ├── 📄 reports.py        # التقارير
│   └── 📄 api.py            # واجهة برمجة التطبيقات
├── 📁 templates/            # قوالب HTML
│   ├── 📄 base.html         # القالب الأساسي
│   ├── 📄 index.html        # الصفحة الرئيسية
│   ├── 📁 auth/             # قوالب المصادقة
│   ├── 📁 customers/        # قوالب العملاء
│   ├── 📁 vehicles/         # قوالب المركبات
│   ├── 📁 services/         # قوالب الخدمات
│   ├── 📁 inventory/        # قوالب المخزون
│   ├── 📁 reports/          # قوالب التقارير
│   └── 📁 errors/           # صفحات الأخطاء
├── 📁 static/               # الملفات الثابتة
│   ├── 📁 css/
│   │   └── 📄 custom.css    # تنسيقات مخصصة
│   ├── 📁 js/
│   │   └── 📄 app.js        # JavaScript مخصص
│   └── 📁 uploads/          # ملفات مرفوعة
├── 📁 instance/             # قاعدة البيانات
├── 📁 logs/                 # ملفات السجل
└── 📁 backups/              # النسخ الاحتياطية
```

## 🔧 أدوات النظام

### أداة التثبيت
```bash
python install.py
```
تقوم بتثبيت وإعداد النظام تلقائياً

### أداة التحديث والصيانة
```bash
python update_system.py --all
```
تقوم بصيانة وتحديث النظام

### خيارات التشغيل
```bash
# التشغيل العادي
python run_final.py

# التشغيل على Windows
start.bat

# التشغيل على Linux/Mac
./start.sh
```

## 📊 لوحة التحكم

النظام يوفر لوحة تحكم شاملة تعرض:
- إحصائيات الخدمات اليومية والشهرية
- تنبيهات المخزون المنخفض
- الخدمات المعلقة والجارية
- الإيرادات والأرباح
- أداء الفنيين

## 🔐 الأمان والصلاحيات

- نظام تسجيل دخول آمن
- تشفير كلمات المرور
- إدارة الصلاحيات (مدير، مشرف، مستخدم)
- جلسات آمنة
- سجلات النشاط

## 📱 التوافق

- **المتصفحات**: Chrome, Firefox, Safari, Edge
- **الأجهزة**: Desktop, Tablet, Mobile
- **أنظمة التشغيل**: Windows, Linux, macOS
- **قواعد البيانات**: SQLite (افتراضي), MySQL, PostgreSQL

## 🛠️ التخصيص والتطوير

النظام مصمم ليكون قابل للتخصيص والتوسع:
- إضافة حقول مخصصة
- تخصيص التقارير
- إضافة وحدات جديدة
- تكامل مع أنظمة خارجية

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع ملف `QUICKSTART.md` للبدء السريع
2. اقرأ `FEATURES.md` لقائمة المميزات التفصيلية
3. تحقق من `FINAL_INSTRUCTIONS.md` للتعليمات النهائية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- **Flask**: إطار العمل الرائع
- **Bootstrap**: للتصميم المتجاوب
- **Chart.js**: للرسوم البيانية
- **Bootstrap Icons**: للأيقونات الجميلة

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: ديسمبر 2024  
**الإصدار**: 1.0.0

**استمتع باستخدام النظام! 🚗💨**
