{% extends "base.html" %}

{% block title %}قائمة المركبات - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-car-front"></i>
        إدارة المركبات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('vehicles.add') }}" class="btn btn-primary">
            <i class="bi bi-car-front-fill"></i>
            إضافة مركبة جديدة
        </a>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-3">
    <div class="col-md-8">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="البحث بالماركة أو الموديل أو رقم اللوحة أو اسم العميل..." 
                   value="{{ search }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('vehicles.index') }}" class="btn btn-outline-danger ms-2">
                <i class="bi bi-x"></i>
            </a>
            {% endif %}
        </form>
    </div>
</div>

<!-- Vehicles Table -->
<div class="card">
    <div class="card-body">
        {% if vehicles.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>المركبة</th>
                        <th>العميل</th>
                        <th>رقم اللوحة</th>
                        <th>السنة</th>
                        <th>عدد الخدمات</th>
                        <th>آخر خدمة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vehicle in vehicles.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-car-front-fill text-primary fs-4"></i>
                                </div>
                                <div>
                                    <strong>{{ vehicle.make }} {{ vehicle.model }}</strong>
                                    {% if vehicle.color %}
                                    <br><small class="text-muted">{{ vehicle.color }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="{{ url_for('customers.view', id=vehicle.customer.id) }}" 
                               class="text-decoration-none">
                                {{ vehicle.customer.name }}
                            </a>
                            <br><small class="text-muted">{{ vehicle.customer.phone }}</small>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ vehicle.plate_number }}</span>
                        </td>
                        <td>{{ vehicle.year or '-' }}</td>
                        <td>
                            <span class="badge bg-info">{{ vehicle.services|length }}</span>
                        </td>
                        <td>
                            {% if vehicle.services %}
                                {% set last_service = vehicle.services|sort(attribute='requested_date', reverse=true)|first %}
                                {{ last_service.requested_date.strftime('%Y-%m-%d') }}
                                <br><small class="text-muted">{{ last_service.service_type or 'عام' }}</small>
                            {% else %}
                                <span class="text-muted">لا توجد خدمات</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('vehicles.view', id=vehicle.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('vehicles.edit', id=vehicle.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{{ url_for('services.add') }}?vehicle_id={{ vehicle.id }}" 
                                   class="btn btn-outline-success" title="إضافة خدمة">
                                    <i class="bi bi-tools"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDelete({{ vehicle.id }}, '{{ vehicle.make }} {{ vehicle.model }} - {{ vehicle.plate_number }}')" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if vehicles.pages > 1 %}
        <nav aria-label="صفحات المركبات">
            <ul class="pagination justify-content-center">
                {% if vehicles.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('vehicles.index', page=vehicles.prev_num, search=search) }}">السابق</a>
                </li>
                {% endif %}

                {% for page_num in vehicles.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != vehicles.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('vehicles.index', page=page_num, search=search) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if vehicles.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('vehicles.index', page=vehicles.next_num, search=search) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-car-front display-1 text-muted"></i>
            <h4 class="mt-3">لا توجد مركبات</h4>
            {% if search %}
            <p class="text-muted">لم يتم العثور على مركبات مطابقة لبحثك</p>
            <a href="{{ url_for('vehicles.index') }}" class="btn btn-secondary">عرض جميع المركبات</a>
            {% else %}
            <p class="text-muted">ابدأ بإضافة مركبة جديدة</p>
            <a href="{{ url_for('vehicles.add') }}" class="btn btn-primary">إضافة مركبة جديدة</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المركبة <strong id="vehicleName"></strong>؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(vehicleId, vehicleName) {
    document.getElementById('vehicleName').textContent = vehicleName;
    document.getElementById('deleteForm').action = '/vehicles/' + vehicleId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
