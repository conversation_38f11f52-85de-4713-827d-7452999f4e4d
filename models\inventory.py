from . import db
from datetime import datetime

class Part(db.Model):
    """نموذج قطع الغيار"""
    __tablename__ = 'parts'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات القطعة
    name = db.Column(db.String(100), nullable=False)
    part_number = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(50))  # فئة القطعة
    brand = db.Column(db.String(50))  # الماركة
    
    # معلومات المخزون
    quantity_in_stock = db.Column(db.Integer, default=0)
    minimum_stock = db.Column(db.Integer, default=5)  # الحد الأدنى للمخزون
    unit = db.Column(db.String(20), default='قطعة')  # وحدة القياس
    
    # معلومات السعر
    cost_price = db.Column(db.Float, nullable=False)  # سعر التكلفة
    selling_price = db.Column(db.Float, nullable=False)  # سعر البيع
    
    # معلومات الموردين
    supplier_name = db.Column(db.String(100))
    supplier_contact = db.Column(db.String(100))
    
    # معلومات إضافية
    location = db.Column(db.String(50))  # موقع القطعة في المخزن
    notes = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    service_parts = db.relationship('ServicePart', backref='part', lazy=True)
    stock_movements = db.relationship('StockMovement', backref='part', lazy=True)
    
    def __repr__(self):
        return f'<Part {self.name}>'
    
    @property
    def is_low_stock(self):
        """فحص إذا كان المخزون منخفض"""
        return self.quantity_in_stock <= self.minimum_stock
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'part_number': self.part_number,
            'description': self.description,
            'category': self.category,
            'brand': self.brand,
            'quantity_in_stock': self.quantity_in_stock,
            'minimum_stock': self.minimum_stock,
            'unit': self.unit,
            'cost_price': self.cost_price,
            'selling_price': self.selling_price,
            'supplier_name': self.supplier_name,
            'is_low_stock': self.is_low_stock,
            'is_active': self.is_active
        }


class StockMovement(db.Model):
    """نموذج حركة المخزون"""
    __tablename__ = 'stock_movements'
    
    id = db.Column(db.Integer, primary_key=True)
    part_id = db.Column(db.Integer, db.ForeignKey('parts.id'), nullable=False)
    
    # تفاصيل الحركة
    movement_type = db.Column(db.String(20), nullable=False)  # in, out, adjustment
    quantity = db.Column(db.Integer, nullable=False)
    reference_number = db.Column(db.String(50))  # رقم مرجعي (فاتورة، طلب صيانة، إلخ)
    notes = db.Column(db.Text)
    
    # معلومات المستخدم
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<StockMovement {self.movement_type} {self.quantity}>'
