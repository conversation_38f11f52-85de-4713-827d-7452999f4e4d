# 🚗 نظام إدارة مركز صيانة السيارات

نظام شامل لإدارة مراكز صيانة السيارات مطور بلغة Python باستخدام Flask مع واجهة مستخدم عربية متجاوبة باستخدام Bootstrap.

## ✨ المميزات الرئيسية

### 👥 إدارة العملاء
- تسجيل بيانات العملاء (الاسم، العنوان، رقم الهاتف، البريد الإلكتروني)
- سجل تاريخي للصيانة لكل عميل
- البحث والفلترة المتقدمة

### 🚙 إدارة المركبات
- تسجيل بيانات المركبة (نوع السيارة، الموديل، رقم اللوحة، رقم الهيكل)
- تتبع سجل الصيانة والإصلاحات لكل مركبة
- إمكانية رفع صور المركبة قبل وبعد الصيانة

### 🔧 إدارة الأعمال الفنية
- تسجيل تفاصيل الأعطال وطلبات الصيانة
- تخصيص الفنيين للأعمال المختلفة
- تتبع حالة العمل (قيد الفحص، قيد الصيانة، مكتمل)
- إدارة الأولويات والمواعيد

### 📦 إدارة قطع الغيار والمخزون
- تسجيل وإدارة قطع الغيار
- تتبع الكميات المتاحة وتنبيه عند انخفاض المخزون
- تسجيل طلبات الشراء من الموردين
- تتبع حركة المخزون

### 💰 إدارة الفواتير والمدفوعات
- إنشاء فواتير تلقائية لطلبات الصيانة
- إدارة طرق الدفع (نقداً، بطاقة ائتمان، تحويل بنكي)
- تتبع المدفوعات المستلمة والمستحقة

### 📊 تقارير وتحليلات
- تقارير عن الإيرادات والمصروفات
- تقارير عن أداء الفنيين
- تقارير عن أكثر الأعطال شيوعاً
- إحصائيات شاملة

### 🎨 واجهة مستخدم متميزة
- تصميم بسيط وسهل للمستخدمين غير الفنيين
- دعم كامل للغة العربية (RTL)
- واجهة متجاوبة تعمل على الحاسوب والهاتف المحمول
- استخدام Bootstrap 5 للتصميم

## 🛠️ التقنيات المستخدمة

- **Backend**: Python Flask
- **Database**: SQLite مع SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, JavaScript
- **UI Framework**: Bootstrap 5 (RTL)
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF

## 📋 متطلبات النظام

- Python 3.8 أو أحدث
- pip (مدير حزم Python)

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd car-center
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python test_app.py
```

أو للنسخة الكاملة:
```bash
python run.py
```

### 4. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## 👤 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
car_center/
├── app.py                 # التطبيق الرئيسي
├── config.py             # إعدادات التطبيق
├── requirements.txt      # المتطلبات
├── run.py               # ملف التشغيل
├── test_app.py          # ملف اختبار بسيط
├── models/              # نماذج قاعدة البيانات
│   ├── __init__.py
│   ├── customer.py      # نموذج العملاء
│   ├── vehicle.py       # نموذج المركبات
│   ├── service.py       # نموذج الخدمات
│   ├── inventory.py     # نموذج المخزون
│   └── invoice.py       # نموذج الفواتير
├── routes/              # المسارات
│   ├── __init__.py
│   ├── auth.py          # المصادقة
│   ├── customers.py     # إدارة العملاء
│   ├── vehicles.py      # إدارة المركبات
│   ├── services.py      # إدارة الخدمات
│   ├── inventory.py     # إدارة المخزون
│   └── reports.py       # التقارير
├── templates/           # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── auth/
│   ├── customers/
│   ├── vehicles/
│   ├── services/
│   └── reports/
├── static/             # الملفات الثابتة
│   ├── css/
│   ├── js/
│   └── uploads/
└── instance/           # قاعدة البيانات
    └── database.db
```

## 🔧 الإعدادات

يمكن تخصيص الإعدادات في ملف `config.py`:

- إعدادات قاعدة البيانات
- إعدادات رفع الملفات
- إعدادات الأمان
- إعدادات التطبيق

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا النظام بواسطة Augment Agent** 🤖
