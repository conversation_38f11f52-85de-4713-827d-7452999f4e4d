from . import db
from datetime import datetime

class Vehicle(db.Model):
    """نموذج المركبات"""
    __tablename__ = 'vehicles'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('customers.id'), nullable=False)
    
    # بيانات المركبة
    make = db.Column(db.String(50), nullable=False)  # الماركة
    model = db.Column(db.String(50), nullable=False)  # الموديل
    year = db.Column(db.Integer)  # سنة الصنع
    color = db.Column(db.String(30))  # اللون
    plate_number = db.Column(db.String(20), unique=True, nullable=False)  # رقم اللوحة
    chassis_number = db.Column(db.String(50))  # رقم الهيكل
    engine_number = db.Column(db.String(50))  # رقم المحرك
    mileage = db.Column(db.Integer)  # عداد الكيلومترات
    
    # معلومات إضافية
    fuel_type = db.Column(db.String(20))  # نوع الوقود
    transmission = db.Column(db.String(20))  # نوع ناقل الحركة
    notes = db.Column(db.Text)
    
    # صور المركبة
    images = db.Column(db.Text)  # JSON string للصور
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    services = db.relationship('Service', backref='vehicle', lazy=True)
    
    def __repr__(self):
        return f'<Vehicle {self.make} {self.model} - {self.plate_number}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'customer_id': self.customer_id,
            'make': self.make,
            'model': self.model,
            'year': self.year,
            'color': self.color,
            'plate_number': self.plate_number,
            'chassis_number': self.chassis_number,
            'engine_number': self.engine_number,
            'mileage': self.mileage,
            'fuel_type': self.fuel_type,
            'transmission': self.transmission,
            'notes': self.notes,
            'services_count': len(self.services)
        }
