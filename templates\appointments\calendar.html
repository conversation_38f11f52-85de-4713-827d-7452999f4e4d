{% extends "base.html" %}

{% block title %}تقويم المواعيد - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-calendar-event"></i>
        تقويم المواعيد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAppointmentModal">
                <i class="bi bi-plus-circle"></i>
                موعد جديد
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="goToToday()">
                <i class="bi bi-calendar-day"></i>
                اليوم
            </button>
            <button type="button" class="btn btn-outline-info" onclick="refreshCalendar()">
                <i class="bi bi-arrow-clockwise"></i>
                تحديث
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-secondary" onclick="changeView('month')">شهر</button>
            <button type="button" class="btn btn-outline-secondary" onclick="changeView('week')">أسبوع</button>
            <button type="button" class="btn btn-outline-secondary" onclick="changeView('day')">يوم</button>
        </div>
    </div>
</div>

<!-- شريط التنقل في التقويم -->
<div class="row mb-3">
    <div class="col-md-6">
        <div class="btn-group">
            <button type="button" class="btn btn-outline-secondary" onclick="navigateCalendar('prev')">
                <i class="bi bi-chevron-right"></i>
                السابق
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="navigateCalendar('next')">
                التالي
                <i class="bi bi-chevron-left"></i>
            </button>
        </div>
        <span class="ms-3 h5" id="currentPeriod">ديسمبر 2024</span>
    </div>
    <div class="col-md-6 text-end">
        <div class="legend">
            <span class="badge bg-success me-2">مؤكد</span>
            <span class="badge bg-warning me-2">معلق</span>
            <span class="badge bg-danger me-2">ملغي</span>
            <span class="badge bg-info">مكتمل</span>
        </div>
    </div>
</div>

<!-- التقويم الرئيسي -->
<div class="row">
    <div class="col-lg-9">
        <div class="card">
            <div class="card-body">
                <div id="calendar"></div>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-3">
        <!-- مواعيد اليوم -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock"></i>
                    مواعيد اليوم
                </h5>
            </div>
            <div class="card-body">
                <div class="appointments-today">
                    <div class="appointment-item">
                        <div class="appointment-time">09:00</div>
                        <div class="appointment-details">
                            <strong>أحمد محمد</strong>
                            <br><small class="text-muted">تويوتا كامري - تغيير زيت</small>
                            <br><span class="badge bg-success">مؤكد</span>
                        </div>
                    </div>
                    
                    <div class="appointment-item">
                        <div class="appointment-time">11:30</div>
                        <div class="appointment-details">
                            <strong>سعد العتيبي</strong>
                            <br><small class="text-muted">هوندا أكورد - فحص شامل</small>
                            <br><span class="badge bg-warning">معلق</span>
                        </div>
                    </div>
                    
                    <div class="appointment-item">
                        <div class="appointment-time">14:00</div>
                        <div class="appointment-details">
                            <strong>محمد الأحمد</strong>
                            <br><small class="text-muted">نيسان التيما - إصلاح فرامل</small>
                            <br><span class="badge bg-success">مؤكد</span>
                        </div>
                    </div>
                    
                    <div class="appointment-item">
                        <div class="appointment-time">16:30</div>
                        <div class="appointment-details">
                            <strong>عبدالله السعد</strong>
                            <br><small class="text-muted">فورد إكسبلورر - صيانة دورية</small>
                            <br><span class="badge bg-success">مؤكد</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    إحصائيات المواعيد
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary">24</h4>
                        <small class="text-muted">هذا الأسبوع</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">18</h4>
                        <small class="text-muted">مؤكدة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">4</h4>
                        <small class="text-muted">معلقة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-danger">2</h4>
                        <small class="text-muted">ملغية</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- الفنيون المتاحون -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people"></i>
                    الفنيون المتاحون
                </h5>
            </div>
            <div class="card-body">
                <div class="technicians-list">
                    <div class="technician-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>أحمد محمد</strong>
                                <br><small class="text-muted">فني محركات</small>
                            </div>
                            <span class="badge bg-success">متاح</span>
                        </div>
                    </div>
                    
                    <div class="technician-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>سعد العتيبي</strong>
                                <br><small class="text-muted">فني كهرباء</small>
                            </div>
                            <span class="badge bg-warning">مشغول</span>
                        </div>
                    </div>
                    
                    <div class="technician-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>محمد الأحمد</strong>
                                <br><small class="text-muted">فني فرامل</small>
                            </div>
                            <span class="badge bg-success">متاح</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة موعد جديد -->
<div class="modal fade" id="addAppointmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة موعد جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="appointmentForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_select" class="form-label">العميل <span class="text-danger">*</span></label>
                            <select class="form-select" id="customer_select" name="customer_id" required>
                                <option value="">اختر العميل</option>
                                <option value="1">أحمد محمد - 0501234567</option>
                                <option value="2">سعد العتيبي - 0509876543</option>
                                <option value="3">محمد الأحمد - 0551234567</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="vehicle_select" class="form-label">المركبة <span class="text-danger">*</span></label>
                            <select class="form-select" id="vehicle_select" name="vehicle_id" required>
                                <option value="">اختر المركبة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="appointment_date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="appointment_date" name="date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="appointment_time" class="form-label">الوقت <span class="text-danger">*</span></label>
                            <select class="form-select" id="appointment_time" name="time" required>
                                <option value="">اختر الوقت</option>
                                <option value="08:00">08:00 صباحاً</option>
                                <option value="08:30">08:30 صباحاً</option>
                                <option value="09:00">09:00 صباحاً</option>
                                <option value="09:30">09:30 صباحاً</option>
                                <option value="10:00">10:00 صباحاً</option>
                                <option value="10:30">10:30 صباحاً</option>
                                <option value="11:00">11:00 صباحاً</option>
                                <option value="11:30">11:30 صباحاً</option>
                                <option value="12:00">12:00 ظهراً</option>
                                <option value="13:00">01:00 ظهراً</option>
                                <option value="13:30">01:30 ظهراً</option>
                                <option value="14:00">02:00 ظهراً</option>
                                <option value="14:30">02:30 ظهراً</option>
                                <option value="15:00">03:00 ظهراً</option>
                                <option value="15:30">03:30 ظهراً</option>
                                <option value="16:00">04:00 ظهراً</option>
                                <option value="16:30">04:30 ظهراً</option>
                                <option value="17:00">05:00 مساءً</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="service_type" class="form-label">نوع الخدمة</label>
                            <select class="form-select" id="service_type" name="service_type">
                                <option value="">اختر نوع الخدمة</option>
                                <option value="صيانة دورية">صيانة دورية</option>
                                <option value="تغيير زيت">تغيير زيت</option>
                                <option value="فحص شامل">فحص شامل</option>
                                <option value="إصلاح فرامل">إصلاح فرامل</option>
                                <option value="إصلاح محرك">إصلاح محرك</option>
                                <option value="إصلاح كهرباء">إصلاح كهرباء</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="technician_select" class="form-label">الفني المفضل</label>
                            <select class="form-select" id="technician_select" name="technician_id">
                                <option value="">اختر الفني</option>
                                <option value="1">أحمد محمد - فني محركات</option>
                                <option value="2">سعد العتيبي - فني كهرباء</option>
                                <option value="3">محمد الأحمد - فني فرامل</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="estimated_duration" class="form-label">المدة المتوقعة (بالساعات)</label>
                        <select class="form-select" id="estimated_duration" name="duration">
                            <option value="1">ساعة واحدة</option>
                            <option value="2" selected>ساعتان</option>
                            <option value="3">3 ساعات</option>
                            <option value="4">4 ساعات</option>
                            <option value="6">6 ساعات</option>
                            <option value="8">يوم كامل</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appointment_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="appointment_notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية حول الموعد..."></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="send_reminder" name="send_reminder" checked>
                        <label class="form-check-label" for="send_reminder">
                            إرسال تذكير للعميل قبل الموعد بـ 24 ساعة
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الموعد</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تفاصيل الموعد -->
<div class="modal fade" id="appointmentDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="appointmentDetailsContent">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-warning" onclick="editAppointment()">تعديل</button>
                <button type="button" class="btn btn-success" onclick="confirmAppointment()">تأكيد</button>
                <button type="button" class="btn btn-danger" onclick="cancelAppointment()">إلغاء</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
#calendar {
    direction: ltr;
}

.appointment-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.appointment-item:last-child {
    border-bottom: none;
}

.appointment-time {
    font-weight: bold;
    color: #495057;
    margin-left: 1rem;
    min-width: 60px;
}

.appointment-details {
    flex: 1;
}

.technician-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.technician-item:last-child {
    border-bottom: none;
}

.legend .badge {
    font-size: 0.75rem;
}

.fc-event {
    border: none !important;
    padding: 2px 4px;
}

.fc-event-confirmed {
    background-color: #28a745 !important;
}

.fc-event-pending {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.fc-event-cancelled {
    background-color: #dc3545 !important;
}

.fc-event-completed {
    background-color: #17a2b8 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script>
let calendar;

document.addEventListener('DOMContentLoaded', function() {
    const calendarEl = document.getElementById('calendar');
    
    calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        locale: 'ar',
        direction: 'rtl',
        headerToolbar: false,
        height: 600,
        events: [
            {
                id: '1',
                title: 'أحمد محمد - تغيير زيت',
                start: '2024-12-15T09:00:00',
                end: '2024-12-15T11:00:00',
                className: 'fc-event-confirmed',
                extendedProps: {
                    customer: 'أحمد محمد',
                    vehicle: 'تويوتا كامري',
                    service: 'تغيير زيت',
                    technician: 'محمد الأحمد',
                    status: 'confirmed'
                }
            },
            {
                id: '2',
                title: 'سعد العتيبي - فحص شامل',
                start: '2024-12-15T11:30:00',
                end: '2024-12-15T15:30:00',
                className: 'fc-event-pending',
                extendedProps: {
                    customer: 'سعد العتيبي',
                    vehicle: 'هوندا أكورد',
                    service: 'فحص شامل',
                    technician: 'أحمد محمد',
                    status: 'pending'
                }
            },
            {
                id: '3',
                title: 'محمد الأحمد - إصلاح فرامل',
                start: '2024-12-15T14:00:00',
                end: '2024-12-15T17:00:00',
                className: 'fc-event-confirmed',
                extendedProps: {
                    customer: 'محمد الأحمد',
                    vehicle: 'نيسان التيما',
                    service: 'إصلاح فرامل',
                    technician: 'سعد العتيبي',
                    status: 'confirmed'
                }
            }
        ],
        eventClick: function(info) {
            showAppointmentDetails(info.event);
        },
        dateClick: function(info) {
            document.getElementById('appointment_date').value = info.dateStr;
            new bootstrap.Modal(document.getElementById('addAppointmentModal')).show();
        }
    });
    
    calendar.render();
});

// وظائف التقويم
function changeView(view) {
    calendar.changeView(view === 'month' ? 'dayGridMonth' : 
                       view === 'week' ? 'timeGridWeek' : 'timeGridDay');
}

function navigateCalendar(direction) {
    if (direction === 'prev') {
        calendar.prev();
    } else {
        calendar.next();
    }
    updateCurrentPeriod();
}

function goToToday() {
    calendar.today();
    updateCurrentPeriod();
}

function refreshCalendar() {
    calendar.refetchEvents();
}

function updateCurrentPeriod() {
    const currentDate = calendar.getDate();
    const view = calendar.view;
    let periodText = '';
    
    if (view.type === 'dayGridMonth') {
        periodText = currentDate.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
    } else if (view.type === 'timeGridWeek') {
        periodText = 'أسبوع ' + currentDate.toLocaleDateString('ar-SA');
    } else {
        periodText = currentDate.toLocaleDateString('ar-SA', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    }
    
    document.getElementById('currentPeriod').textContent = periodText;
}

// وظائف المواعيد
function showAppointmentDetails(event) {
    const details = `
        <div class="appointment-details">
            <h6><strong>العميل:</strong> ${event.extendedProps.customer}</h6>
            <p><strong>المركبة:</strong> ${event.extendedProps.vehicle}</p>
            <p><strong>نوع الخدمة:</strong> ${event.extendedProps.service}</p>
            <p><strong>الفني:</strong> ${event.extendedProps.technician}</p>
            <p><strong>التاريخ والوقت:</strong> ${event.start.toLocaleDateString('ar-SA')} - ${event.start.toLocaleTimeString('ar-SA', {hour: '2-digit', minute:'2-digit'})}</p>
            <p><strong>الحالة:</strong> 
                <span class="badge ${event.extendedProps.status === 'confirmed' ? 'bg-success' : 
                                   event.extendedProps.status === 'pending' ? 'bg-warning' : 'bg-danger'}">
                    ${event.extendedProps.status === 'confirmed' ? 'مؤكد' : 
                      event.extendedProps.status === 'pending' ? 'معلق' : 'ملغي'}
                </span>
            </p>
        </div>
    `;
    
    document.getElementById('appointmentDetailsContent').innerHTML = details;
    new bootstrap.Modal(document.getElementById('appointmentDetailsModal')).show();
}

// تحديث المركبات عند اختيار العميل
document.getElementById('customer_select').addEventListener('change', function() {
    const customerId = this.value;
    const vehicleSelect = document.getElementById('vehicle_select');
    
    vehicleSelect.innerHTML = '<option value="">اختر المركبة</option>';
    
    if (customerId) {
        // في التطبيق الحقيقي، سيتم جلب المركبات من API
        const vehicles = {
            '1': [
                {id: 1, name: 'تويوتا كامري 2020 - أ ب ج 123'},
                {id: 2, name: 'هوندا أكورد 2019 - د هـ و 456'}
            ],
            '2': [
                {id: 3, name: 'نيسان التيما 2021 - ز ح ط 789'}
            ],
            '3': [
                {id: 4, name: 'فورد إكسبلورر 2018 - ي ك ل 012'}
            ]
        };
        
        if (vehicles[customerId]) {
            vehicles[customerId].forEach(vehicle => {
                const option = document.createElement('option');
                option.value = vehicle.id;
                option.textContent = vehicle.name;
                vehicleSelect.appendChild(option);
            });
        }
    }
});

// حفظ الموعد
document.getElementById('appointmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const appointmentData = Object.fromEntries(formData);
    
    // في التطبيق الحقيقي، سيتم إرسال البيانات للخادم
    console.log('بيانات الموعد:', appointmentData);
    
    // إضافة الموعد للتقويم
    calendar.addEvent({
        title: `${appointmentData.customer_id} - ${appointmentData.service_type}`,
        start: `${appointmentData.date}T${appointmentData.time}:00`,
        className: 'fc-event-pending'
    });
    
    // إغلاق المودال
    bootstrap.Modal.getInstance(document.getElementById('addAppointmentModal')).hide();
    
    // إعادة تعيين النموذج
    this.reset();
    
    alert('تم حفظ الموعد بنجاح');
});

// وظائف إدارة المواعيد
function editAppointment() {
    alert('سيتم فتح نموذج التعديل');
}

function confirmAppointment() {
    alert('تم تأكيد الموعد');
    bootstrap.Modal.getInstance(document.getElementById('appointmentDetailsModal')).hide();
}

function cancelAppointment() {
    if (confirm('هل أنت متأكد من إلغاء هذا الموعد؟')) {
        alert('تم إلغاء الموعد');
        bootstrap.Modal.getInstance(document.getElementById('appointmentDetailsModal')).hide();
    }
}

// تحديث التاريخ الافتراضي لليوم الحالي
document.getElementById('appointment_date').value = new Date().toISOString().split('T')[0];
</script>
{% endblock %}
