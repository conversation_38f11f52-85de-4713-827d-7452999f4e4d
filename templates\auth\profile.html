{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-person-circle"></i>
        الملف الشخصي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
            <i class="bi bi-key"></i>
            تغيير كلمة المرور
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">معلومات المستخدم</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td width="30%"><strong>الاسم الكامل:</strong></td>
                        <td>{{ current_user.full_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>اسم المستخدم:</strong></td>
                        <td>{{ current_user.username }}</td>
                    </tr>
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>{{ current_user.email }}</td>
                    </tr>
                    <tr>
                        <td><strong>الدور:</strong></td>
                        <td>
                            <span class="badge 
                                {% if current_user.role == 'admin' %}bg-danger
                                {% elif current_user.role == 'manager' %}bg-warning
                                {% else %}bg-info{% endif %}">
                                {% if current_user.role == 'admin' %}مدير النظام
                                {% elif current_user.role == 'manager' %}مدير
                                {% else %}مستخدم{% endif %}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>حالة الحساب:</strong></td>
                        <td>
                            {% if current_user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الإنشاء:</strong></td>
                        <td>{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-shield-check"></i>
                    الأمان
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                        <i class="bi bi-key"></i>
                        تغيير كلمة المرور
                    </a>
                </div>
                
                <hr>
                
                <h6>نصائح الأمان:</h6>
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        استخدم كلمة مرور قوية
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        لا تشارك بيانات الدخول
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        سجل الخروج عند الانتهاء
                    </li>
                </ul>
            </div>
        </div>

        {% if current_user.role == 'admin' %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    إدارة النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-people"></i>
                        إدارة المستخدمين
                    </a>
                    <a href="{{ url_for('auth.add_user') }}" class="btn btn-outline-success">
                        <i class="bi bi-person-plus"></i>
                        إضافة مستخدم جديد
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Activity Summary -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-activity"></i>
                    ملخص النشاط
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-primary">{{ session_info.login_time or 'غير متاح' }}</h4>
                            <small class="text-muted">وقت تسجيل الدخول</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-success">{{ current_user.role }}</h4>
                            <small class="text-muted">مستوى الصلاحية</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-info">نشط</h4>
                            <small class="text-muted">حالة الجلسة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ current_user.created_at.strftime('%Y') }}</h4>
                        <small class="text-muted">عضو منذ</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
