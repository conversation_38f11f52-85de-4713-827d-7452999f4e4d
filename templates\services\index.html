{% extends "base.html" %}

{% block title %}قائمة الخدمات - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-tools"></i>
        إدارة الخدمات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('services.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i>
            إضافة خدمة جديدة
        </a>
    </div>
</div>

<!-- Filters -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="البحث برقم الخدمة أو اسم العميل أو رقم اللوحة..." 
                   value="{{ search }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('services.index') }}" class="btn btn-outline-danger ms-2">
                <i class="bi bi-x"></i>
            </a>
            {% endif %}
        </form>
    </div>
    <div class="col-md-3">
        <form method="GET">
            {% if search %}<input type="hidden" name="search" value="{{ search }}">{% endif %}
            <select class="form-select" name="status" onchange="this.form.submit()">
                <option value="">جميع الحالات</option>
                <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلق</option>
                <option value="in_progress" {% if status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتمل</option>
                <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغي</option>
            </select>
        </form>
    </div>
</div>

<!-- Services Table -->
<div class="card">
    <div class="card-body">
        {% if services.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الخدمة</th>
                        <th>العميل</th>
                        <th>المركبة</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                        <th>الأولوية</th>
                        <th>الفني</th>
                        <th>التكلفة</th>
                        <th>التاريخ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for service in services.items %}
                    <tr>
                        <td>
                            <strong>{{ service.service_number }}</strong>
                        </td>
                        <td>
                            <a href="{{ url_for('customers.view', id=service.customer.id) }}" 
                               class="text-decoration-none">
                                {{ service.customer.name }}
                            </a>
                        </td>
                        <td>
                            <a href="{{ url_for('vehicles.view', id=service.vehicle.id) }}" 
                               class="text-decoration-none">
                                {{ service.vehicle.make }} {{ service.vehicle.model }}
                            </a>
                            <br><small class="text-muted">{{ service.vehicle.plate_number }}</small>
                        </td>
                        <td>
                            <span title="{{ service.description }}">
                                {{ service.description[:40] }}{% if service.description|length > 40 %}...{% endif %}
                            </span>
                            {% if service.service_type %}
                            <br><small class="text-muted">{{ service.service_type }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge status-badge 
                                {% if service.status == 'pending' %}bg-warning text-dark
                                {% elif service.status == 'in_progress' %}bg-info
                                {% elif service.status == 'completed' %}bg-success
                                {% elif service.status == 'cancelled' %}bg-danger
                                {% else %}bg-secondary{% endif %}">
                                {% if service.status == 'pending' %}معلق
                                {% elif service.status == 'in_progress' %}قيد التنفيذ
                                {% elif service.status == 'completed' %}مكتمل
                                {% elif service.status == 'cancelled' %}ملغي
                                {% else %}{{ service.status }}{% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="badge 
                                {% if service.priority == 'urgent' %}priority-high
                                {% elif service.priority == 'high' %}priority-high
                                {% elif service.priority == 'medium' %}priority-medium
                                {% else %}priority-low{% endif %}">
                                {% if service.priority == 'urgent' %}عاجل
                                {% elif service.priority == 'high' %}عالي
                                {% elif service.priority == 'medium' %}متوسط
                                {% else %}منخفض{% endif %}
                            </span>
                        </td>
                        <td>
                            {{ service.technician_name or '-' }}
                        </td>
                        <td>
                            {% if service.total_cost > 0 %}
                                {{ "%.2f"|format(service.total_cost) }} ر.س
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ service.requested_date.strftime('%Y-%m-%d') }}
                            {% if service.completed_date %}
                            <br><small class="text-success">
                                اكتمل: {{ service.completed_date.strftime('%m-%d') }}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('services.view', id=service.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('services.edit', id=service.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% if service.status != 'completed' %}
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="updateStatus({{ service.id }}, 'completed')" title="إكمال">
                                    <i class="bi bi-check-circle"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if services.pages > 1 %}
        <nav aria-label="صفحات الخدمات">
            <ul class="pagination justify-content-center">
                {% if services.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('services.index', page=services.prev_num, search=search, status=status) }}">السابق</a>
                </li>
                {% endif %}

                {% for page_num in services.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != services.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('services.index', page=page_num, search=search, status=status) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if services.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('services.index', page=services.next_num, search=search, status=status) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-tools display-1 text-muted"></i>
            <h4 class="mt-3">لا توجد خدمات</h4>
            {% if search or status %}
            <p class="text-muted">لم يتم العثور على خدمات مطابقة للفلاتر المحددة</p>
            <a href="{{ url_for('services.index') }}" class="btn btn-secondary">عرض جميع الخدمات</a>
            {% else %}
            <p class="text-muted">ابدأ بإضافة خدمة جديدة</p>
            <a href="{{ url_for('services.add') }}" class="btn btn-primary">إضافة خدمة جديدة</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">معلق</h5>
                <h3 class="text-warning">{{ services.items|selectattr('status', 'equalto', 'pending')|list|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">قيد التنفيذ</h5>
                <h3 class="text-info">{{ services.items|selectattr('status', 'equalto', 'in_progress')|list|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">مكتمل</h5>
                <h3 class="text-success">{{ services.items|selectattr('status', 'equalto', 'completed')|list|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">الإجمالي</h5>
                <h3 class="text-primary">{{ services.items|length }}</h3>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateStatus(serviceId, newStatus) {
    if (confirm('هل أنت متأكد من تغيير حالة الخدمة؟')) {
        // يمكن إضافة AJAX request هنا
        window.location.href = `/services/${serviceId}/edit`;
    }
}
</script>
{% endblock %}
