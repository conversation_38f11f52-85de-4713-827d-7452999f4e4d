import os
from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import LoginManager, login_required, current_user
from werkzeug.security import generate_password_hash
from config import config
from models import db, User
from models.customer import Customer
from models.vehicle import Vehicle
from models.service import Service, ServicePart
from models.inventory import Part, StockMovement
from models.invoice import Invoice, Payment

def create_app(config_name=None):
    """إنشاء تطبيق Flask"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    # تهيئة نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # إنشاء المجلدات المطلوبة
    os.makedirs('instance', exist_ok=True)
    os.makedirs('static/uploads', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    
    # تسجيل المسارات
    from routes.auth import auth_bp
    from routes.customers import customers_bp
    from routes.vehicles import vehicles_bp
    from routes.services import services_bp
    from routes.inventory import inventory_bp
    from routes.reports import reports_bp
    
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(vehicles_bp, url_prefix='/vehicles')
    app.register_blueprint(services_bp, url_prefix='/services')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    
    # الصفحة الرئيسية
    @app.route('/')
    @login_required
    def index():
        # إحصائيات سريعة للوحة التحكم
        stats = {
            'customers_count': Customer.query.count(),
            'vehicles_count': Vehicle.query.count(),
            'pending_services': Service.query.filter_by(status='pending').count(),
            'in_progress_services': Service.query.filter_by(status='in_progress').count(),
            'low_stock_parts': Part.query.filter(Part.quantity_in_stock <= Part.minimum_stock).count(),
            'unpaid_invoices': Invoice.query.filter_by(payment_status='pending').count()
        }
        
        # آخر الخدمات
        recent_services = Service.query.order_by(Service.created_at.desc()).limit(5).all()
        
        # قطع الغيار منخفضة المخزون
        low_stock_parts = Part.query.filter(Part.quantity_in_stock <= Part.minimum_stock).limit(5).all()
        
        return render_template('index.html', 
                             stats=stats, 
                             recent_services=recent_services,
                             low_stock_parts=low_stock_parts)
    
    # إنشاء قاعدة البيانات والمستخدم الافتراضي
    with app.app_context():
        db.create_all()

        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                full_name='مدير النظام',
                role='admin'
            )
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
