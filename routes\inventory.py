from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db
from models.inventory import Part, StockMovement

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/')
@login_required
def index():
    """قائمة قطع الغيار"""
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '', type=str)
    search = request.args.get('search', '', type=str)
    low_stock = request.args.get('low_stock', False, type=bool)
    
    query = Part.query.filter_by(is_active=True)
    
    if category:
        query = query.filter(Part.category == category)
    
    if search:
        query = query.filter(
            Part.name.contains(search) |
            Part.part_number.contains(search) |
            Part.brand.contains(search)
        )
    
    if low_stock:
        query = query.filter(Part.quantity_in_stock <= Part.minimum_stock)
    
    parts = query.order_by(Part.name).paginate(
        page=page, per_page=15, error_out=False
    )
    
    # جلب الفئات للفلترة
    categories = db.session.query(Part.category).filter(Part.category.isnot(None)).distinct().all()
    categories = [cat[0] for cat in categories]
    
    return render_template('inventory/index.html', 
                         parts=parts, 
                         categories=categories,
                         selected_category=category,
                         search=search,
                         low_stock=low_stock)

@inventory_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة قطعة غيار جديدة"""
    if request.method == 'POST':
        name = request.form.get('name')
        part_number = request.form.get('part_number')
        description = request.form.get('description')
        category = request.form.get('category')
        brand = request.form.get('brand')
        quantity_in_stock = request.form.get('quantity_in_stock')
        minimum_stock = request.form.get('minimum_stock')
        unit = request.form.get('unit')
        cost_price = request.form.get('cost_price')
        selling_price = request.form.get('selling_price')
        supplier_name = request.form.get('supplier_name')
        supplier_contact = request.form.get('supplier_contact')
        location = request.form.get('location')
        notes = request.form.get('notes')
        
        # التحقق من البيانات المطلوبة
        if not name or not part_number or not cost_price or not selling_price:
            flash('الاسم ورقم القطعة وأسعار التكلفة والبيع مطلوبة', 'error')
            return render_template('inventory/add.html')
        
        # التحقق من عدم تكرار رقم القطعة
        if Part.query.filter_by(part_number=part_number).first():
            flash('رقم القطعة موجود بالفعل', 'error')
            return render_template('inventory/add.html')
        
        # إنشاء قطعة الغيار الجديدة
        part = Part(
            name=name,
            part_number=part_number,
            description=description,
            category=category,
            brand=brand,
            quantity_in_stock=int(quantity_in_stock) if quantity_in_stock else 0,
            minimum_stock=int(minimum_stock) if minimum_stock else 5,
            unit=unit or 'قطعة',
            cost_price=float(cost_price),
            selling_price=float(selling_price),
            supplier_name=supplier_name,
            supplier_contact=supplier_contact,
            location=location,
            notes=notes
        )
        
        db.session.add(part)
        db.session.commit()
        
        # إضافة حركة مخزون إذا كانت الكمية أكبر من صفر
        if part.quantity_in_stock > 0:
            stock_movement = StockMovement(
                part_id=part.id,
                movement_type='in',
                quantity=part.quantity_in_stock,
                reference_number='INITIAL_STOCK',
                notes='رصيد افتتاحي',
                user_id=current_user.id
            )
            db.session.add(stock_movement)
            db.session.commit()
        
        flash('تم إضافة قطعة الغيار بنجاح', 'success')
        return redirect(url_for('inventory.view', id=part.id))
    
    return render_template('inventory/add.html')

@inventory_bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل قطعة الغيار"""
    part = Part.query.get_or_404(id)
    
    # جلب تاريخ حركة المخزون
    movements = StockMovement.query.filter_by(part_id=id).order_by(StockMovement.created_at.desc()).limit(20).all()
    
    return render_template('inventory/view.html', part=part, movements=movements)

@inventory_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل قطعة الغيار"""
    part = Part.query.get_or_404(id)
    
    if request.method == 'POST':
        name = request.form.get('name')
        part_number = request.form.get('part_number')
        description = request.form.get('description')
        category = request.form.get('category')
        brand = request.form.get('brand')
        minimum_stock = request.form.get('minimum_stock')
        unit = request.form.get('unit')
        cost_price = request.form.get('cost_price')
        selling_price = request.form.get('selling_price')
        supplier_name = request.form.get('supplier_name')
        supplier_contact = request.form.get('supplier_contact')
        location = request.form.get('location')
        notes = request.form.get('notes')
        is_active = bool(request.form.get('is_active'))
        
        # التحقق من البيانات المطلوبة
        if not name or not part_number or not cost_price or not selling_price:
            flash('الاسم ورقم القطعة وأسعار التكلفة والبيع مطلوبة', 'error')
            return render_template('inventory/edit.html', part=part)
        
        # التحقق من عدم تكرار رقم القطعة (باستثناء القطعة الحالية)
        existing_part = Part.query.filter_by(part_number=part_number).first()
        if existing_part and existing_part.id != part.id:
            flash('رقم القطعة موجود بالفعل', 'error')
            return render_template('inventory/edit.html', part=part)
        
        # تحديث البيانات
        part.name = name
        part.part_number = part_number
        part.description = description
        part.category = category
        part.brand = brand
        part.minimum_stock = int(minimum_stock) if minimum_stock else 5
        part.unit = unit or 'قطعة'
        part.cost_price = float(cost_price)
        part.selling_price = float(selling_price)
        part.supplier_name = supplier_name
        part.supplier_contact = supplier_contact
        part.location = location
        part.notes = notes
        part.is_active = is_active
        
        db.session.commit()
        flash('تم تحديث قطعة الغيار بنجاح', 'success')
        return redirect(url_for('inventory.view', id=part.id))
    
    return render_template('inventory/edit.html', part=part)

@inventory_bp.route('/<int:id>/adjust_stock', methods=['POST'])
@login_required
def adjust_stock(id):
    """تعديل كمية المخزون"""
    part = Part.query.get_or_404(id)
    
    movement_type = request.form.get('movement_type')  # in, out, adjustment
    quantity = request.form.get('quantity')
    reference_number = request.form.get('reference_number')
    notes = request.form.get('notes')
    
    if not movement_type or not quantity:
        flash('نوع الحركة والكمية مطلوبان', 'error')
        return redirect(url_for('inventory.view', id=id))
    
    quantity = int(quantity)
    
    # التحقق من صحة الحركة
    if movement_type == 'out' and part.quantity_in_stock < quantity:
        flash('الكمية المطلوبة أكبر من المتاح في المخزون', 'error')
        return redirect(url_for('inventory.view', id=id))
    
    # تحديث المخزون
    if movement_type == 'in':
        part.quantity_in_stock += quantity
    elif movement_type == 'out':
        part.quantity_in_stock -= quantity
    elif movement_type == 'adjustment':
        # في حالة التعديل، الكمية المدخلة هي الكمية الجديدة
        old_quantity = part.quantity_in_stock
        part.quantity_in_stock = quantity
        quantity = quantity - old_quantity  # الفرق للتسجيل
    
    # إضافة حركة المخزون
    stock_movement = StockMovement(
        part_id=id,
        movement_type=movement_type,
        quantity=abs(quantity),
        reference_number=reference_number,
        notes=notes,
        user_id=current_user.id
    )
    
    db.session.add(stock_movement)
    db.session.commit()
    
    flash('تم تحديث المخزون بنجاح', 'success')
    return redirect(url_for('inventory.view', id=id))

@inventory_bp.route('/low_stock')
@login_required
def low_stock():
    """قطع الغيار منخفضة المخزون"""
    parts = Part.query.filter(
        Part.quantity_in_stock <= Part.minimum_stock,
        Part.is_active == True
    ).order_by(Part.quantity_in_stock).all()
    
    return render_template('inventory/low_stock.html', parts=parts)

@inventory_bp.route('/api/search')
@login_required
def api_search():
    """البحث عن قطع الغيار عبر API"""
    query = request.args.get('q', '')
    
    if len(query) < 2:
        return jsonify([])
    
    parts = Part.query.filter(
        Part.is_active == True,
        (Part.name.contains(query) | Part.part_number.contains(query))
    ).limit(10).all()
    
    results = []
    for part in parts:
        results.append({
            'id': part.id,
            'name': part.name,
            'part_number': part.part_number,
            'selling_price': part.selling_price,
            'quantity_in_stock': part.quantity_in_stock,
            'unit': part.unit
        })
    
    return jsonify(results)
