{% extends "base.html" %}

{% block title %}قائمة العملاء - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-people"></i>
        إدارة العملاء
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('customers.add') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i>
            إضافة عميل جديد
        </a>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="البحث بالاسم أو الهاتف أو البريد الإلكتروني..." 
                   value="{{ search }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('customers.index') }}" class="btn btn-outline-danger ms-2">
                <i class="bi bi-x"></i>
            </a>
            {% endif %}
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-body">
        {% if customers.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>عدد المركبات</th>
                        <th>عدد الخدمات</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers.items %}
                    <tr>
                        <td>
                            <strong>{{ customer.name }}</strong>
                            {% if customer.national_id %}
                            <br><small class="text-muted">{{ customer.national_id }}</small>
                            {% endif %}
                        </td>
                        <td>{{ customer.phone }}</td>
                        <td>{{ customer.email or '-' }}</td>
                        <td>
                            <span class="badge bg-primary">{{ customer.vehicles|length }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ customer.services|length }}</span>
                        </td>
                        <td>{{ customer.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('customers.view', id=customer.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('customers.edit', id=customer.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDelete({{ customer.id }}, '{{ customer.name }}')" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if customers.pages > 1 %}
        <nav aria-label="صفحات العملاء">
            <ul class="pagination justify-content-center">
                {% if customers.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('customers.index', page=customers.prev_num, search=search) }}">السابق</a>
                </li>
                {% endif %}

                {% for page_num in customers.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != customers.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers.index', page=page_num, search=search) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if customers.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('customers.index', page=customers.next_num, search=search) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h4 class="mt-3">لا يوجد عملاء</h4>
            {% if search %}
            <p class="text-muted">لم يتم العثور على عملاء مطابقين لبحثك</p>
            <a href="{{ url_for('customers.index') }}" class="btn btn-secondary">عرض جميع العملاء</a>
            {% else %}
            <p class="text-muted">ابدأ بإضافة عميل جديد</p>
            <a href="{{ url_for('customers.add') }}" class="btn btn-primary">إضافة عميل جديد</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العميل <strong id="customerName"></strong>؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(customerId, customerName) {
    document.getElementById('customerName').textContent = customerName;
    document.getElementById('deleteForm').action = '/customers/' + customerId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
