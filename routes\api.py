"""
API routes for AJAX requests and data exchange
"""

from flask import Blueprint, jsonify, request
from models import db, Customer, Vehicle, Part, Service
from datetime import datetime, timedelta
import json

api = Blueprint('api', __name__, url_prefix='/api')

@api.route('/customers/search')
def search_customers():
    """البحث في العملاء"""
    query = request.args.get('q', '').strip()
    
    if len(query) < 2:
        return jsonify([])
    
    customers = Customer.query.filter(
        Customer.name.contains(query) |
        Customer.phone.contains(query) |
        Customer.email.contains(query)
    ).limit(10).all()
    
    results = []
    for customer in customers:
        results.append({
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email,
            'details': f"{customer.phone} - {customer.email or 'لا يوجد بريد'}"
        })
    
    return jsonify(results)

@api.route('/vehicles/by_customer/<int:customer_id>')
def get_customer_vehicles(customer_id):
    """جلب مركبات عميل معين"""
    vehicles = Vehicle.query.filter_by(customer_id=customer_id).all()
    
    results = []
    for vehicle in vehicles:
        results.append({
            'id': vehicle.id,
            'display_name': f"{vehicle.make} {vehicle.model} - {vehicle.plate_number}",
            'make': vehicle.make,
            'model': vehicle.model,
            'plate_number': vehicle.plate_number,
            'year': vehicle.year
        })
    
    return jsonify(results)

@api.route('/inventory/search')
def search_parts():
    """البحث في قطع الغيار"""
    query = request.args.get('q', '').strip()
    
    if len(query) < 1:
        # إرجاع جميع القطع النشطة إذا لم يكن هناك بحث
        parts = Part.query.filter_by(is_active=True).limit(50).all()
    else:
        parts = Part.query.filter(
            Part.is_active == True,
            (Part.name.contains(query) |
             Part.part_number.contains(query) |
             Part.brand.contains(query))
        ).limit(20).all()
    
    results = []
    for part in parts:
        results.append({
            'id': part.id,
            'name': part.name,
            'part_number': part.part_number,
            'brand': part.brand,
            'selling_price': float(part.selling_price),
            'cost_price': float(part.cost_price),
            'quantity_in_stock': part.quantity_in_stock,
            'unit': part.unit,
            'is_low_stock': part.is_low_stock
        })
    
    return jsonify(results)

@api.route('/dashboard/stats')
def dashboard_stats():
    """إحصائيات لوحة التحكم"""
    from datetime import date
    
    today = date.today()
    this_month_start = today.replace(day=1)
    
    # إحصائيات الخدمات
    today_services = Service.query.filter(
        Service.requested_date >= today
    ).count()
    
    pending_services = Service.query.filter_by(status='pending').count()
    
    # إحصائيات المخزون
    low_stock_parts = Part.query.filter(
        Part.quantity_in_stock <= Part.minimum_stock,
        Part.is_active == True
    ).count()
    
    # إيرادات الشهر (تقديرية)
    this_month_services = Service.query.filter(
        Service.requested_date >= this_month_start,
        Service.status == 'completed'
    ).all()
    
    this_month_revenue = sum(service.total_cost for service in this_month_services)
    
    return jsonify({
        'today_services': today_services,
        'pending_services': pending_services,
        'low_stock_alerts': low_stock_parts,
        'this_month_revenue': float(this_month_revenue)
    })

@api.route('/services/update_status/<int:service_id>', methods=['POST'])
def update_service_status(service_id):
    """تحديث حالة الخدمة"""
    service = Service.query.get_or_404(service_id)
    data = request.get_json()
    
    new_status = data.get('status')
    if new_status not in ['pending', 'in_progress', 'completed', 'cancelled']:
        return jsonify({'error': 'حالة غير صحيحة'}), 400
    
    service.status = new_status
    
    # تحديث التواريخ حسب الحالة
    if new_status == 'in_progress' and not service.started_date:
        service.started_date = datetime.now()
    elif new_status == 'completed' and not service.completed_date:
        service.completed_date = datetime.now()
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'تم تحديث حالة الخدمة بنجاح',
        'new_status': new_status
    })

@api.route('/inventory/low_stock')
def get_low_stock_parts():
    """جلب قطع الغيار منخفضة المخزون"""
    parts = Part.query.filter(
        Part.quantity_in_stock <= Part.minimum_stock,
        Part.is_active == True
    ).all()
    
    results = []
    for part in parts:
        needed_quantity = max(0, part.minimum_stock * 2 - part.quantity_in_stock)
        estimated_cost = needed_quantity * part.cost_price
        
        results.append({
            'id': part.id,
            'name': part.name,
            'part_number': part.part_number,
            'current_stock': part.quantity_in_stock,
            'minimum_stock': part.minimum_stock,
            'needed_quantity': needed_quantity,
            'estimated_cost': float(estimated_cost),
            'supplier_name': part.supplier_name,
            'supplier_contact': part.supplier_contact,
            'priority': 'urgent' if part.quantity_in_stock == 0 else 'high' if part.quantity_in_stock <= part.minimum_stock / 2 else 'medium'
        })
    
    # ترتيب حسب الأولوية
    results.sort(key=lambda x: (x['priority'] == 'urgent', x['priority'] == 'high', x['needed_quantity']), reverse=True)
    
    return jsonify(results)

@api.route('/reports/monthly_revenue')
def monthly_revenue():
    """إيرادات شهرية للرسم البياني"""
    from sqlalchemy import func, extract
    
    # جلب الإيرادات الشهرية لآخر 12 شهر
    monthly_data = db.session.query(
        extract('year', Service.completed_date).label('year'),
        extract('month', Service.completed_date).label('month'),
        func.sum(Service.total_cost).label('revenue'),
        func.count(Service.id).label('count')
    ).filter(
        Service.status == 'completed',
        Service.completed_date.isnot(None)
    ).group_by(
        extract('year', Service.completed_date),
        extract('month', Service.completed_date)
    ).order_by(
        extract('year', Service.completed_date),
        extract('month', Service.completed_date)
    ).limit(12).all()
    
    results = []
    for data in monthly_data:
        month_names = [
            '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        
        results.append({
            'year': int(data.year),
            'month': int(data.month),
            'month_name': f"{month_names[int(data.month)]} {int(data.year)}",
            'revenue': float(data.revenue or 0),
            'count': int(data.count or 0)
        })
    
    return jsonify(results)

@api.route('/customers/<int:customer_id>/summary')
def customer_summary(customer_id):
    """ملخص العميل"""
    customer = Customer.query.get_or_404(customer_id)
    
    # إحصائيات العميل
    total_vehicles = len(customer.vehicles)
    total_services = len(customer.services)
    completed_services = len([s for s in customer.services if s.status == 'completed'])
    total_spent = sum(s.total_cost for s in customer.services if s.status == 'completed')
    
    # آخر خدمة
    last_service = None
    if customer.services:
        last_service_obj = max(customer.services, key=lambda s: s.requested_date)
        last_service = {
            'id': last_service_obj.id,
            'service_number': last_service_obj.service_number,
            'date': last_service_obj.requested_date.strftime('%Y-%m-%d'),
            'status': last_service_obj.status,
            'vehicle': f"{last_service_obj.vehicle.make} {last_service_obj.vehicle.model}"
        }
    
    return jsonify({
        'customer_id': customer.id,
        'name': customer.name,
        'phone': customer.phone,
        'email': customer.email,
        'total_vehicles': total_vehicles,
        'total_services': total_services,
        'completed_services': completed_services,
        'total_spent': float(total_spent),
        'last_service': last_service,
        'member_since': customer.created_at.strftime('%Y-%m-%d')
    })

@api.route('/vehicles/<int:vehicle_id>/service_history')
def vehicle_service_history(vehicle_id):
    """تاريخ خدمات المركبة"""
    vehicle = Vehicle.query.get_or_404(vehicle_id)
    
    services = []
    for service in vehicle.services:
        services.append({
            'id': service.id,
            'service_number': service.service_number,
            'description': service.description,
            'service_type': service.service_type,
            'status': service.status,
            'technician_name': service.technician_name,
            'requested_date': service.requested_date.strftime('%Y-%m-%d'),
            'completed_date': service.completed_date.strftime('%Y-%m-%d') if service.completed_date else None,
            'total_cost': float(service.total_cost),
            'parts_used': len(service.service_parts)
        })
    
    # ترتيب حسب التاريخ (الأحدث أولاً)
    services.sort(key=lambda s: s['requested_date'], reverse=True)
    
    return jsonify({
        'vehicle_id': vehicle.id,
        'vehicle_info': f"{vehicle.make} {vehicle.model} - {vehicle.plate_number}",
        'total_services': len(services),
        'services': services
    })

@api.errorhandler(404)
def api_not_found(error):
    return jsonify({'error': 'المورد غير موجود'}), 404

@api.errorhandler(500)
def api_internal_error(error):
    return jsonify({'error': 'خطأ داخلي في الخادم'}), 500
