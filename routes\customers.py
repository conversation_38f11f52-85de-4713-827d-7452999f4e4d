from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from models import db
from models.customer import Customer
from models.vehicle import Vehicle
from models.service import Service

customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/')
@login_required
def index():
    """قائمة العملاء"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Customer.query
    
    if search:
        query = query.filter(
            Customer.name.contains(search) |
            Customer.phone.contains(search) |
            Customer.email.contains(search)
        )
    
    customers = query.order_by(Customer.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('customers/index.html', customers=customers, search=search)

@customers_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة عميل جديد"""
    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        national_id = request.form.get('national_id')
        notes = request.form.get('notes')
        
        # التحقق من البيانات المطلوبة
        if not name or not phone:
            flash('الاسم ورقم الهاتف مطلوبان', 'error')
            return render_template('customers/add.html')
        
        # التحقق من عدم تكرار رقم الهاتف
        if Customer.query.filter_by(phone=phone).first():
            flash('رقم الهاتف موجود بالفعل', 'error')
            return render_template('customers/add.html')
        
        # إنشاء العميل الجديد
        customer = Customer(
            name=name,
            phone=phone,
            email=email,
            address=address,
            national_id=national_id,
            notes=notes
        )
        
        db.session.add(customer)
        db.session.commit()
        flash('تم إضافة العميل بنجاح', 'success')
        return redirect(url_for('customers.view', id=customer.id))
    
    return render_template('customers/add.html')

@customers_bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل العميل"""
    customer = Customer.query.get_or_404(id)
    
    # جلب المركبات والخدمات
    vehicles = Vehicle.query.filter_by(customer_id=id).all()
    services = Service.query.filter_by(customer_id=id).order_by(Service.created_at.desc()).limit(10).all()
    
    return render_template('customers/view.html', 
                         customer=customer, 
                         vehicles=vehicles, 
                         services=services)

@customers_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل بيانات العميل"""
    customer = Customer.query.get_or_404(id)
    
    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        national_id = request.form.get('national_id')
        notes = request.form.get('notes')
        
        # التحقق من البيانات المطلوبة
        if not name or not phone:
            flash('الاسم ورقم الهاتف مطلوبان', 'error')
            return render_template('customers/edit.html', customer=customer)
        
        # التحقق من عدم تكرار رقم الهاتف (باستثناء العميل الحالي)
        existing_customer = Customer.query.filter_by(phone=phone).first()
        if existing_customer and existing_customer.id != customer.id:
            flash('رقم الهاتف موجود بالفعل', 'error')
            return render_template('customers/edit.html', customer=customer)
        
        # تحديث البيانات
        customer.name = name
        customer.phone = phone
        customer.email = email
        customer.address = address
        customer.national_id = national_id
        customer.notes = notes
        
        db.session.commit()
        flash('تم تحديث بيانات العميل بنجاح', 'success')
        return redirect(url_for('customers.view', id=customer.id))
    
    return render_template('customers/edit.html', customer=customer)

@customers_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف العميل"""
    customer = Customer.query.get_or_404(id)
    
    # التحقق من وجود مركبات أو خدمات مرتبطة
    if customer.vehicles or customer.services:
        flash('لا يمكن حذف العميل لوجود مركبات أو خدمات مرتبطة به', 'error')
        return redirect(url_for('customers.view', id=customer.id))
    
    db.session.delete(customer)
    db.session.commit()
    flash('تم حذف العميل بنجاح', 'success')
    return redirect(url_for('customers.index'))

@customers_bp.route('/api/search')
@login_required
def api_search():
    """البحث عن العملاء عبر API"""
    query = request.args.get('q', '')
    
    if len(query) < 2:
        return jsonify([])
    
    customers = Customer.query.filter(
        Customer.name.contains(query) |
        Customer.phone.contains(query)
    ).limit(10).all()
    
    results = []
    for customer in customers:
        results.append({
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email
        })
    
    return jsonify(results)
