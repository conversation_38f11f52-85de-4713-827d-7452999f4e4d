/* تخصيصات CSS لنظام إدارة مركز صيانة السيارات */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* تحسين الألوان */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* تحسين الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, #343a40 0%, #495057 100%);
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1.25rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255,255,255,0.1);
    transform: translateX(-2px);
}

.sidebar .nav-link.active {
    color: #fff;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* تحسين البطاقات الإحصائية */
.stats-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.stats-card.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.stats-card.danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

/* تحسين الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.875rem 0.75rem;
    border-color: #e9ecef;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--primary-color), 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* تحسين الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #146c43 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
    color: var(--dark-color);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #b02a37 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0aa2c0 100%);
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
    font-weight: 600;
}

/* تحسين الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-weight: 500;
}

.status-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* أولويات الخدمات */
.priority-high {
    background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.priority-medium {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.priority-low {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* تحسين النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.25rem;
    border-left: 4px solid;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d1edff 0%, #e7f3ff 100%);
    border-left-color: var(--success-color);
    color: #0f5132;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left-color: var(--warning-color);
    color: #664d03;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c2c7 100%);
    border-left-color: var(--danger-color);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-left-color: var(--info-color);
    color: #055160;
}

/* تحسين الشريط العلوي */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

/* تحسين التنقل */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-color: var(--primary-color);
}

/* تحسين المودال */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0;
}

/* تحسين الأيقونات */
.bi {
    vertical-align: -0.125em;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسين الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* تحسين التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسين الانتقالات */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسين التركيز */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}
