{% extends "base.html" %}

{% block title %}{{ vehicle.make }} {{ vehicle.model }} - تفاصيل المركبة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-car-front-fill"></i>
        {{ vehicle.make }} {{ vehicle.model }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('vehicles.edit', id=vehicle.id) }}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i>
                تعديل
            </a>
            <a href="{{ url_for('services.add') }}?vehicle_id={{ vehicle.id }}" class="btn btn-success">
                <i class="bi bi-tools"></i>
                إضافة خدمة
            </a>
            <button type="button" class="btn btn-info" onclick="printVehicle()">
                <i class="bi bi-printer"></i>
                طباعة
            </button>
        </div>
        <a href="{{ url_for('vehicles.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للمركبات
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات المركبة -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات المركبة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الماركة:</strong></td>
                                <td>{{ vehicle.make }}</td>
                            </tr>
                            <tr>
                                <td><strong>الموديل:</strong></td>
                                <td>{{ vehicle.model }}</td>
                            </tr>
                            <tr>
                                <td><strong>سنة الصنع:</strong></td>
                                <td>{{ vehicle.year or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>اللون:</strong></td>
                                <td>{{ vehicle.color or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم اللوحة:</strong></td>
                                <td><span class="badge bg-secondary fs-6">{{ vehicle.plate_number }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهيكل:</strong></td>
                                <td><code>{{ vehicle.chassis_number or 'غير محدد' }}</code></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم المحرك:</strong></td>
                                <td><code>{{ vehicle.engine_number or 'غير محدد' }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>عداد الكيلومترات:</strong></td>
                                <td>
                                    {% if vehicle.mileage %}
                                        {{ "{:,}".format(vehicle.mileage) }} كم
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>نوع الوقود:</strong></td>
                                <td>{{ vehicle.fuel_type or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>ناقل الحركة:</strong></td>
                                <td>{{ vehicle.transmission or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التسجيل:</strong></td>
                                <td>{{ vehicle.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ vehicle.updated_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if vehicle.notes %}
                <div class="mt-3">
                    <h6><strong>ملاحظات:</strong></h6>
                    <p class="text-muted">{{ vehicle.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- معلومات العميل -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-circle"></i>
                    معلومات المالك
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <table class="table table-borderless mb-0">
                            <tr>
                                <td><strong>اسم المالك:</strong></td>
                                <td>{{ vehicle.customer.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>
                                    <a href="tel:{{ vehicle.customer.phone }}" class="text-decoration-none">
                                        {{ vehicle.customer.phone }}
                                    </a>
                                </td>
                            </tr>
                            {% if vehicle.customer.email %}
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>
                                    <a href="mailto:{{ vehicle.customer.email }}" class="text-decoration-none">
                                        {{ vehicle.customer.email }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('customers.view', id=vehicle.customer.id) }}" class="btn btn-outline-primary">
                            <i class="bi bi-eye"></i>
                            عرض ملف العميل
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- تاريخ الخدمات -->
        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    تاريخ الخدمات ({{ services|length }})
                </h5>
                <a href="{{ url_for('services.add') }}?vehicle_id={{ vehicle.id }}" class="btn btn-sm btn-success">
                    <i class="bi bi-plus"></i>
                    إضافة خدمة
                </a>
            </div>
            <div class="card-body">
                {% if services %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الخدمة</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>الفني</th>
                                <th>التكلفة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in services %}
                            <tr>
                                <td><strong>{{ service.service_number }}</strong></td>
                                <td>
                                    <span title="{{ service.description }}">
                                        {{ service.description[:40] }}{% if service.description|length > 40 %}...{% endif %}
                                    </span>
                                    {% if service.service_type %}
                                    <br><small class="text-muted">{{ service.service_type }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge status-badge 
                                        {% if service.status == 'pending' %}bg-warning text-dark
                                        {% elif service.status == 'in_progress' %}bg-info
                                        {% elif service.status == 'completed' %}bg-success
                                        {% elif service.status == 'cancelled' %}bg-danger
                                        {% else %}bg-secondary{% endif %}">
                                        {% if service.status == 'pending' %}معلق
                                        {% elif service.status == 'in_progress' %}قيد التنفيذ
                                        {% elif service.status == 'completed' %}مكتمل
                                        {% elif service.status == 'cancelled' %}ملغي
                                        {% else %}{{ service.status }}{% endif %}
                                    </span>
                                </td>
                                <td>{{ service.technician_name or '-' }}</td>
                                <td>
                                    {% if service.total_cost > 0 %}
                                        {{ "%.2f"|format(service.total_cost) }} ر.س
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>{{ service.requested_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('services.view', id=service.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-tools display-4 text-muted"></i>
                    <h5 class="mt-3">لا توجد خدمات</h5>
                    <p class="text-muted">لم يتم تسجيل أي خدمة لهذه المركبة بعد</p>
                    <a href="{{ url_for('services.add') }}?vehicle_id={{ vehicle.id }}" class="btn btn-success">
                        <i class="bi bi-plus"></i>
                        إضافة خدمة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الملخص والإحصائيات -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    إحصائيات المركبة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary">{{ services|length }}</h4>
                            <small class="text-muted">إجمالي الخدمات</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ services|selectattr('status', 'equalto', 'completed')|list|length }}</h4>
                        <small class="text-muted">خدمات مكتملة</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info">{{ services|selectattr('status', 'equalto', 'pending')|list|length }}</h4>
                            <small class="text-muted">خدمات معلقة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">
                            {{ "%.0f"|format(services|map(attribute='total_cost')|sum) }}
                        </h4>
                        <small class="text-muted">إجمالي التكلفة (ر.س)</small>
                    </div>
                </div>
            </div>
        </div>

        {% if services %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-check"></i>
                    آخر خدمة
                </h5>
            </div>
            <div class="card-body">
                {% set last_service = services|sort(attribute='requested_date', reverse=true)|first %}
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>رقم الخدمة:</strong></td>
                        <td>{{ last_service.service_number }}</td>
                    </tr>
                    <tr>
                        <td><strong>التاريخ:</strong></td>
                        <td>{{ last_service.requested_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>النوع:</strong></td>
                        <td>{{ last_service.service_type or 'عام' }}</td>
                    </tr>
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            <span class="badge 
                                {% if last_service.status == 'completed' %}bg-success
                                {% elif last_service.status == 'in_progress' %}bg-info
                                {% else %}bg-warning{% endif %}">
                                {% if last_service.status == 'completed' %}مكتمل
                                {% elif last_service.status == 'in_progress' %}قيد التنفيذ
                                {% else %}معلق{% endif %}
                            </span>
                        </td>
                    </tr>
                </table>
                <div class="d-grid">
                    <a href="{{ url_for('services.view', id=last_service.id) }}" class="btn btn-outline-primary btn-sm">
                        عرض تفاصيل الخدمة
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('services.add') }}?vehicle_id={{ vehicle.id }}" class="btn btn-success btn-sm">
                        <i class="bi bi-tools"></i>
                        إضافة خدمة جديدة
                    </a>
                    
                    <a href="{{ url_for('vehicles.edit', id=vehicle.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-pencil"></i>
                        تعديل بيانات المركبة
                    </a>
                    
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="updateMileage()">
                        <i class="bi bi-speedometer2"></i>
                        تحديث عداد الكيلومترات
                    </button>
                    
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printVehicle()">
                        <i class="bi bi-printer"></i>
                        طباعة بيانات المركبة
                    </button>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-shield-check"></i>
                    تذكيرات الصيانة
                </h5>
            </div>
            <div class="card-body">
                {% if vehicle.mileage %}
                    {% set next_oil_change = ((vehicle.mileage // 5000) + 1) * 5000 %}
                    {% set oil_remaining = next_oil_change - vehicle.mileage %}
                    
                    <div class="alert alert-info alert-sm">
                        <i class="bi bi-droplet"></i>
                        <strong>تغيير الزيت:</strong><br>
                        بعد {{ oil_remaining }} كم<br>
                        <small class="text-muted">عند {{ "{:,}".format(next_oil_change) }} كم</small>
                    </div>
                {% endif %}
                
                <div class="alert alert-warning alert-sm">
                    <i class="bi bi-calendar"></i>
                    <strong>فحص دوري:</strong><br>
                    يُنصح بفحص دوري كل 6 أشهر
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تحديث عداد الكيلومترات -->
<div class="modal fade" id="mileageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث عداد الكيلومترات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('vehicles.update_mileage', id=vehicle.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_mileage" class="form-label">القراءة الجديدة للعداد</label>
                        <input type="number" class="form-control" id="new_mileage" name="mileage" 
                               min="{{ vehicle.mileage or 0 }}" value="{{ vehicle.mileage or 0 }}" required>
                        <div class="form-text">
                            القراءة الحالية: {{ "{:,}".format(vehicle.mileage) if vehicle.mileage else 'غير محدد' }} كم
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" 
                                  placeholder="ملاحظات حول تحديث العداد..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateMileage() {
    new bootstrap.Modal(document.getElementById('mileageModal')).show();
}

function printVehicle() {
    window.print();
}

// التحقق من صحة قراءة العداد
document.getElementById('new_mileage').addEventListener('input', function() {
    const currentMileage = {{ vehicle.mileage or 0 }};
    const newMileage = parseInt(this.value);
    
    if (newMileage < currentMileage) {
        this.setCustomValidity('القراءة الجديدة يجب أن تكون أكبر من القراءة الحالية');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
