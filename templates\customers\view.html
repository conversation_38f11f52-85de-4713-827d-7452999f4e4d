{% extends "base.html" %}

{% block title %}{{ customer.name }} - تفاصيل العميل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-person-circle"></i>
        {{ customer.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('customers.edit', id=customer.id) }}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i>
                تعديل
            </a>
            <a href="{{ url_for('vehicles.add') }}?customer_id={{ customer.id }}" class="btn btn-success">
                <i class="bi bi-car-front-fill"></i>
                إضافة مركبة
            </a>
            <a href="{{ url_for('services.add') }}?customer_id={{ customer.id }}" class="btn btn-info">
                <i class="bi bi-tools"></i>
                إضافة خدمة
            </a>
        </div>
        <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات العميل -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>الاسم:</strong></td>
                        <td>{{ customer.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>رقم الهاتف:</strong></td>
                        <td>
                            <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                {{ customer.phone }}
                            </a>
                        </td>
                    </tr>
                    {% if customer.email %}
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>
                            <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                {{ customer.email }}
                            </a>
                        </td>
                    </tr>
                    {% endif %}
                    {% if customer.national_id %}
                    <tr>
                        <td><strong>رقم الهوية:</strong></td>
                        <td>{{ customer.national_id }}</td>
                    </tr>
                    {% endif %}
                    {% if customer.address %}
                    <tr>
                        <td><strong>العنوان:</strong></td>
                        <td>{{ customer.address }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>تاريخ التسجيل:</strong></td>
                        <td>{{ customer.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                </table>
                
                {% if customer.notes %}
                <div class="mt-3">
                    <strong>ملاحظات:</strong>
                    <p class="text-muted mt-2">{{ customer.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ vehicles|length }}</h4>
                            <small class="text-muted">مركبة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ services|length }}</h4>
                        <small class="text-muted">خدمة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المركبات والخدمات -->
    <div class="col-lg-8">
        <!-- المركبات -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-car-front"></i>
                    المركبات ({{ vehicles|length }})
                </h5>
                <a href="{{ url_for('vehicles.add') }}?customer_id={{ customer.id }}" class="btn btn-sm btn-success">
                    <i class="bi bi-plus"></i>
                    إضافة مركبة
                </a>
            </div>
            <div class="card-body">
                {% if vehicles %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الماركة والموديل</th>
                                <th>رقم اللوحة</th>
                                <th>السنة</th>
                                <th>عدد الخدمات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for vehicle in vehicles %}
                            <tr>
                                <td>
                                    <strong>{{ vehicle.make }} {{ vehicle.model }}</strong>
                                    {% if vehicle.color %}
                                    <br><small class="text-muted">{{ vehicle.color }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ vehicle.plate_number }}</td>
                                <td>{{ vehicle.year or '-' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ vehicle.services|length }}</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('vehicles.view', id=vehicle.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ url_for('services.add') }}?vehicle_id={{ vehicle.id }}" 
                                           class="btn btn-outline-success" title="إضافة خدمة">
                                            <i class="bi bi-tools"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-car-front display-4 text-muted"></i>
                    <h5 class="mt-3">لا توجد مركبات</h5>
                    <p class="text-muted">لم يتم تسجيل أي مركبة لهذا العميل بعد</p>
                    <a href="{{ url_for('vehicles.add') }}?customer_id={{ customer.id }}" class="btn btn-success">
                        <i class="bi bi-plus"></i>
                        إضافة مركبة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- آخر الخدمات -->
        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    آخر الخدمات
                </h5>
                <a href="{{ url_for('services.add') }}?customer_id={{ customer.id }}" class="btn btn-sm btn-info">
                    <i class="bi bi-plus"></i>
                    إضافة خدمة
                </a>
            </div>
            <div class="card-body">
                {% if services %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الخدمة</th>
                                <th>المركبة</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in services %}
                            <tr>
                                <td>{{ service.service_number }}</td>
                                <td>{{ service.vehicle.make }} {{ service.vehicle.model }}</td>
                                <td>{{ service.description[:50] }}{% if service.description|length > 50 %}...{% endif %}</td>
                                <td>
                                    <span class="badge status-badge 
                                        {% if service.status == 'pending' %}bg-warning
                                        {% elif service.status == 'in_progress' %}bg-info
                                        {% elif service.status == 'completed' %}bg-success
                                        {% else %}bg-secondary{% endif %}">
                                        {% if service.status == 'pending' %}معلق
                                        {% elif service.status == 'in_progress' %}قيد التنفيذ
                                        {% elif service.status == 'completed' %}مكتمل
                                        {% else %}{{ service.status }}{% endif %}
                                    </span>
                                </td>
                                <td>{{ service.requested_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('services.view', id=service.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-tools display-4 text-muted"></i>
                    <h5 class="mt-3">لا توجد خدمات</h5>
                    <p class="text-muted">لم يتم تسجيل أي خدمة لهذا العميل بعد</p>
                    <a href="{{ url_for('services.add') }}?customer_id={{ customer.id }}" class="btn btn-info">
                        <i class="bi bi-plus"></i>
                        إضافة خدمة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
