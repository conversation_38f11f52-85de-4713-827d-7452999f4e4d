{% extends "base.html" %}

{% block title %}التقارير المالية - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-graph-up"></i>
        التقارير المالية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-success">
                <i class="bi bi-download"></i>
                تصدير PDF
            </button>
            <button type="button" class="btn btn-outline-info">
                <i class="bi bi-file-earmark-excel"></i>
                تصدير Excel
            </button>
        </div>
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للتقارير
        </a>
    </div>
</div>

<!-- فلاتر التاريخ -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ start_date }}">
                    </div>
                    <div class="col-md-6">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ end_date }}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                            تحديث التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي الإيرادات
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ "%.2f"|format(revenue) }} ر.س
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            فواتير مدفوعة
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ paid_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            فواتير معلقة
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ pending_invoices }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            مبالغ مستحقة
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ "%.2f"|format(pending_amount) }} ر.س
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row">
    <!-- رسم الإيرادات الشهرية -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    الإيرادات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- ملخص الفترة -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-range"></i>
                    ملخص الفترة
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td>من تاريخ:</td>
                        <td><strong>{{ start_date }}</strong></td>
                    </tr>
                    <tr>
                        <td>إلى تاريخ:</td>
                        <td><strong>{{ end_date }}</strong></td>
                    </tr>
                    <tr>
                        <td>عدد الأيام:</td>
                        <td><strong>{{ (end_date - start_date).days + 1 }}</strong></td>
                    </tr>
                    <tr class="table-active">
                        <td><strong>متوسط يومي:</strong></td>
                        <td><strong>{{ "%.2f"|format(revenue / ((end_date - start_date).days + 1)) }} ر.س</strong></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    توزيع الدفعات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="paymentChart" width="300" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- جدول تفصيلي -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    تفاصيل الإيرادات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الشهر</th>
                                <th>الإيرادات</th>
                                <th>عدد الفواتير</th>
                                <th>متوسط الفاتورة</th>
                                <th>النمو %</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for month_data in monthly_revenue %}
                            <tr>
                                <td>{{ month_data.month_name }}</td>
                                <td>{{ "%.2f"|format(month_data.revenue) }} ر.س</td>
                                <td>{{ month_data.invoices_count or 0 }}</td>
                                <td>
                                    {% if month_data.invoices_count and month_data.invoices_count > 0 %}
                                        {{ "%.2f"|format(month_data.revenue / month_data.invoices_count) }} ر.س
                                    {% else %}
                                        0.00 ر.س
                                    {% endif %}
                                </td>
                                <td>
                                    {% if loop.index > 1 %}
                                        {% set prev_revenue = monthly_revenue[loop.index - 2].revenue %}
                                        {% if prev_revenue > 0 %}
                                            {% set growth = ((month_data.revenue - prev_revenue) / prev_revenue * 100) %}
                                            <span class="{% if growth > 0 %}text-success{% elif growth < 0 %}text-danger{% else %}text-muted{% endif %}">
                                                {{ "%.1f"|format(growth) }}%
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-active">
                                <th>الإجمالي</th>
                                <th>{{ "%.2f"|format(monthly_revenue|sum(attribute='revenue')) }} ر.س</th>
                                <th>{{ monthly_revenue|sum(attribute='invoices_count') or 0 }}</th>
                                <th>
                                    {% set total_invoices = monthly_revenue|sum(attribute='invoices_count') or 0 %}
                                    {% if total_invoices > 0 %}
                                        {{ "%.2f"|format((monthly_revenue|sum(attribute='revenue')) / total_invoices) }} ر.س
                                    {% else %}
                                        0.00 ر.س
                                    {% endif %}
                                </th>
                                <th>-</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم الإيرادات الشهرية
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: [
            {% for month_data in monthly_revenue %}
            '{{ month_data.month_name }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'الإيرادات (ر.س)',
            data: [
                {% for month_data in monthly_revenue %}
                {{ month_data.revenue }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'تطور الإيرادات الشهرية'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ر.س';
                    }
                }
            }
        }
    }
});

// رسم توزيع الدفعات
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
const paymentChart = new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: ['مدفوع', 'معلق'],
        datasets: [{
            data: [{{ paid_invoices }}, {{ pending_invoices }}],
            backgroundColor: [
                'rgba(75, 192, 192, 0.8)',
                'rgba(255, 99, 132, 0.8)'
            ],
            borderColor: [
                'rgba(75, 192, 192, 1)',
                'rgba(255, 99, 132, 1)'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'حالة الفواتير'
            },
            legend: {
                position: 'bottom'
            }
        }
    }
});

// تحديث التواريخ تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (!startDate.value) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        startDate.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
    
    if (!endDate.value) {
        const today = new Date();
        endDate.value = today.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
