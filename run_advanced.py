#!/usr/bin/env python3
"""
ملف تشغيل متقدم لنظام إدارة مركز صيانة السيارات
يتضمن إعدادات متقدمة وخيارات إضافية
"""

import os
import sys
import argparse
import webbrowser
import time
import threading
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        sys.exit(1)
    print(f"✅ إصدار Python: {sys.version.split()[0]}")

def check_requirements():
    """فحص المتطلبات"""
    required_packages = [
        'flask',
        'flask_sqlalchemy', 
        'flask_login',
        'flask_wtf',
        'wtforms',
        'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ حزم مفقودة:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 لتثبيت المتطلبات:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'instance',
        'static/uploads',
        'static/css',
        'static/js',
        'logs',
        'backups'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ تم إنشاء المجلدات المطلوبة")

def setup_logging():
    """إعداد نظام السجلات"""
    import logging
    from datetime import datetime
    
    # إنشاء مجلد السجلات
    Path('logs').mkdir(exist_ok=True)
    
    # إعداد السجل
    log_filename = f"logs/app_{datetime.now().strftime('%Y%m%d')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    print(f"✅ تم إعداد نظام السجلات: {log_filename}")

def backup_database():
    """عمل نسخة احتياطية من قاعدة البيانات"""
    import shutil
    from datetime import datetime
    
    db_path = 'instance/car_center.db'
    if os.path.exists(db_path):
        backup_dir = Path('backups')
        backup_dir.mkdir(exist_ok=True)
        
        backup_filename = f"car_center_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = backup_dir / backup_filename
        
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    
    return None

def open_browser_delayed(url, delay=3):
    """فتح المتصفح بعد تأخير"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"🌐 تم فتح المتصفح: {url}")
        except Exception as e:
            print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")
            print(f"يرجى فتح الرابط يدوياً: {url}")
    
    thread = threading.Thread(target=open_browser, daemon=True)
    thread.start()

def run_app(host='0.0.0.0', port=5000, debug=True, auto_browser=True, backup=False):
    """تشغيل التطبيق"""
    
    print("🚗 نظام إدارة مركز صيانة السيارات - الإصدار المتقدم")
    print("=" * 70)
    
    # فحص إصدار Python
    check_python_version()
    
    # فحص المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # إنشاء المجلدات
    create_directories()
    
    # إعداد السجلات
    setup_logging()
    
    # عمل نسخة احتياطية
    if backup:
        backup_database()
    
    try:
        # استيراد التطبيق
        print("📦 جاري تحميل التطبيق...")
        
        # محاولة استيراد التطبيق الكامل أولاً
        try:
            from app import create_app
            app = create_app()
            print("✅ تم تحميل التطبيق الكامل")
        except Exception as e:
            print(f"⚠️ فشل تحميل التطبيق الكامل: {e}")
            print("🔄 جاري تحميل التطبيق البسيط...")
            from test_app import app
            print("✅ تم تحميل التطبيق البسيط")
        
        # معلومات التشغيل
        url = f"http://localhost:{port}"
        print("\n" + "=" * 70)
        print("🚀 تم تشغيل النظام بنجاح!")
        print(f"🌐 الرابط: {url}")
        print("👤 المستخدم الافتراضي: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 70)
        print("💡 اضغط Ctrl+C لإيقاف النظام")
        print("📝 السجلات محفوظة في مجلد logs/")
        if backup:
            print("💾 النسخ الاحتياطية محفوظة في مجلد backups/")
        print("=" * 70)
        
        # فتح المتصفح تلقائياً
        if auto_browser:
            open_browser_delayed(url)
        
        # تشغيل التطبيق
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=False,  # تجنب إعادة التحميل المزدوج
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من:")
        print("   - تثبيت المتطلبات: pip install -r requirements.txt")
        print("   - عدم استخدام المنفذ من تطبيق آخر")
        print("   - صحة ملفات التطبيق")
        sys.exit(1)

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description='نظام إدارة مركز صيانة السيارات',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python run_advanced.py                    # تشغيل عادي
  python run_advanced.py --port 8080        # تغيير المنفذ
  python run_advanced.py --no-debug         # بدون وضع التطوير
  python run_advanced.py --no-browser       # بدون فتح المتصفح
  python run_advanced.py --backup           # مع نسخة احتياطية
  python run_advanced.py --host 127.0.0.1   # تشغيل محلي فقط
        """
    )
    
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='عنوان IP للخادم (افتراضي: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=5000,
        help='رقم المنفذ (افتراضي: 5000)'
    )
    
    parser.add_argument(
        '--no-debug',
        action='store_true',
        help='تعطيل وضع التطوير'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='عدم فتح المتصفح تلقائياً'
    )
    
    parser.add_argument(
        '--backup',
        action='store_true',
        help='عمل نسخة احتياطية قبل التشغيل'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='نظام إدارة مركز صيانة السيارات v1.0.0'
    )
    
    args = parser.parse_args()
    
    # تشغيل التطبيق
    run_app(
        host=args.host,
        port=args.port,
        debug=not args.no_debug,
        auto_browser=not args.no_browser,
        backup=args.backup
    )

if __name__ == '__main__':
    main()
