#!/usr/bin/env python3
"""
ملف اختبار بسيط لنظام إدارة مركز صيانة السيارات
"""

import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from werkzeug.security import generate_password_hash

# إنشاء تطبيق Flask بسيط
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_car_center.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# نموذج المستخدم البسيط
from flask_login import UserMixin

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@app.route('/')
def index():
    return '''
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>نظام إدارة مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="text-center">
                <h1 class="display-4">🚗 نظام إدارة مركز صيانة السيارات</h1>
                <p class="lead">مرحباً بك في النظام</p>
                <div class="alert alert-success">
                    <h4>✅ التطبيق يعمل بنجاح!</h4>
                    <p>تم إنشاء النظام بنجاح ويمكنك الآن البدء في استخدامه</p>
                </div>
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">👥 إدارة العملاء</h5>
                                <p class="card-text">إضافة وإدارة بيانات العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">🚙 إدارة المركبات</h5>
                                <p class="card-text">تسجيل وتتبع المركبات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">🔧 إدارة الخدمات</h5>
                                <p class="card-text">تتبع أعمال الصيانة والإصلاح</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">📦 إدارة المخزون</h5>
                                <p class="card-text">إدارة قطع الغيار والمخزون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">📊 التقارير</h5>
                                <p class="card-text">تقارير مالية وإحصائيات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                full_name='مدير النظام',
                role='admin'
            )
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
    print("🚗 نظام إدارة مركز صيانة السيارات")
    print("=" * 50)
    print("🌐 الرابط: http://localhost:5000")
    print("👤 المستخدم الافتراضي: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 50)
    app.run(debug=True, host='0.0.0.0', port=5000)
