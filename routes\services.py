from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db
from models.customer import Customer
from models.vehicle import Vehicle
from models.service import Service, ServicePart
from models.inventory import Part
from models.invoice import Invoice
from datetime import datetime
import uuid

services_bp = Blueprint('services', __name__)

@services_bp.route('/')
@login_required
def index():
    """قائمة الخدمات"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '', type=str)
    search = request.args.get('search', '', type=str)
    
    query = Service.query.join(Customer).join(Vehicle)
    
    if status:
        query = query.filter(Service.status == status)
    
    if search:
        query = query.filter(
            Service.service_number.contains(search) |
            Service.description.contains(search) |
            Customer.name.contains(search) |
            Vehicle.plate_number.contains(search)
        )
    
    services = query.order_by(Service.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('services/index.html', services=services, status=status, search=search)

@services_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة خدمة جديدة"""
    if request.method == 'POST':
        customer_id = request.form.get('customer_id')
        vehicle_id = request.form.get('vehicle_id')
        description = request.form.get('description')
        service_type = request.form.get('service_type')
        priority = request.form.get('priority')
        technician_name = request.form.get('technician_name')
        labor_cost = request.form.get('labor_cost')
        
        # التحقق من البيانات المطلوبة
        if not customer_id or not vehicle_id or not description:
            flash('العميل والمركبة ووصف الخدمة مطلوبة', 'error')
            customers = Customer.query.order_by(Customer.name).all()
            return render_template('services/add.html', customers=customers)
        
        # إنشاء رقم خدمة فريد
        service_number = f"SRV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # إنشاء الخدمة الجديدة
        service = Service(
            customer_id=customer_id,
            vehicle_id=vehicle_id,
            service_number=service_number,
            description=description,
            service_type=service_type,
            priority=priority or 'medium',
            technician_name=technician_name,
            labor_cost=float(labor_cost) if labor_cost else 0.0
        )
        
        db.session.add(service)
        db.session.commit()
        flash('تم إضافة الخدمة بنجاح', 'success')
        return redirect(url_for('services.view', id=service.id))
    
    customers = Customer.query.order_by(Customer.name).all()
    return render_template('services/add.html', customers=customers)

@services_bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الخدمة"""
    service = Service.query.get_or_404(id)
    
    # جلب قطع الغيار المستخدمة
    service_parts = ServicePart.query.filter_by(service_id=id).all()
    
    # جلب الفاتورة إن وجدت
    invoice = Invoice.query.filter_by(service_id=id).first()
    
    return render_template('services/view.html', 
                         service=service, 
                         service_parts=service_parts,
                         invoice=invoice)

@services_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل الخدمة"""
    service = Service.query.get_or_404(id)
    
    if request.method == 'POST':
        description = request.form.get('description')
        service_type = request.form.get('service_type')
        priority = request.form.get('priority')
        status = request.form.get('status')
        technician_name = request.form.get('technician_name')
        technician_notes = request.form.get('technician_notes')
        labor_cost = request.form.get('labor_cost')
        warranty_period = request.form.get('warranty_period')
        
        # تحديث البيانات
        service.description = description
        service.service_type = service_type
        service.priority = priority
        service.status = status
        service.technician_name = technician_name
        service.technician_notes = technician_notes
        service.labor_cost = float(labor_cost) if labor_cost else 0.0
        service.warranty_period = int(warranty_period) if warranty_period else None
        
        # تحديث التواريخ حسب الحالة
        if status == 'in_progress' and not service.started_date:
            service.started_date = datetime.utcnow()
        elif status == 'completed' and not service.completed_date:
            service.completed_date = datetime.utcnow()
        
        # حساب التكلفة الإجمالية
        service.calculate_total_cost()
        
        db.session.commit()
        flash('تم تحديث الخدمة بنجاح', 'success')
        return redirect(url_for('services.view', id=service.id))
    
    return render_template('services/edit.html', service=service)

@services_bp.route('/<int:id>/add_part', methods=['POST'])
@login_required
def add_part(id):
    """إضافة قطعة غيار للخدمة"""
    service = Service.query.get_or_404(id)
    
    part_id = request.form.get('part_id')
    quantity = request.form.get('quantity')
    unit_price = request.form.get('unit_price')
    
    if not part_id or not quantity or not unit_price:
        flash('جميع البيانات مطلوبة', 'error')
        return redirect(url_for('services.view', id=id))
    
    part = Part.query.get(part_id)
    if not part:
        flash('قطعة الغيار غير موجودة', 'error')
        return redirect(url_for('services.view', id=id))
    
    quantity = int(quantity)
    unit_price = float(unit_price)
    
    # التحقق من توفر الكمية
    if part.quantity_in_stock < quantity:
        flash(f'الكمية المتاحة في المخزون: {part.quantity_in_stock}', 'error')
        return redirect(url_for('services.view', id=id))
    
    # إضافة قطعة الغيار للخدمة
    service_part = ServicePart(
        service_id=id,
        part_id=part_id,
        quantity=quantity,
        unit_price=unit_price,
        total_price=quantity * unit_price
    )
    
    # تحديث المخزون
    part.quantity_in_stock -= quantity
    
    db.session.add(service_part)
    
    # إعادة حساب التكلفة الإجمالية
    service.calculate_total_cost()
    
    db.session.commit()
    flash('تم إضافة قطعة الغيار بنجاح', 'success')
    return redirect(url_for('services.view', id=id))

@services_bp.route('/<int:id>/remove_part/<int:part_id>', methods=['POST'])
@login_required
def remove_part(id, part_id):
    """إزالة قطعة غيار من الخدمة"""
    service_part = ServicePart.query.filter_by(service_id=id, id=part_id).first_or_404()
    
    # إرجاع الكمية للمخزون
    part = service_part.part
    part.quantity_in_stock += service_part.quantity
    
    db.session.delete(service_part)
    
    # إعادة حساب التكلفة الإجمالية
    service = Service.query.get(id)
    service.calculate_total_cost()
    
    db.session.commit()
    flash('تم إزالة قطعة الغيار بنجاح', 'success')
    return redirect(url_for('services.view', id=id))

@services_bp.route('/<int:id>/create_invoice', methods=['POST'])
@login_required
def create_invoice(id):
    """إنشاء فاتورة للخدمة"""
    service = Service.query.get_or_404(id)
    
    # التحقق من عدم وجود فاتورة مسبقاً
    if service.invoice:
        flash('توجد فاتورة لهذه الخدمة بالفعل', 'error')
        return redirect(url_for('services.view', id=id))
    
    # إنشاء رقم فاتورة فريد
    invoice_number = f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    # إنشاء الفاتورة
    invoice = Invoice(
        service_id=id,
        customer_id=service.customer_id,
        invoice_number=invoice_number
    )
    
    # حساب إجماليات الفاتورة
    invoice.calculate_totals()
    
    db.session.add(invoice)
    db.session.commit()
    
    flash('تم إنشاء الفاتورة بنجاح', 'success')
    return redirect(url_for('services.view', id=id))
