@echo off
chcp 65001 >nul
title نظام إدارة مركز صيانة السيارات

echo.
echo ========================================
echo    نظام إدارة مركز صيانة السيارات
echo ========================================
echo.

echo [1/4] فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python متوفر

echo.
echo [2/4] فحص المتطلبات...
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Flask غير مثبت، جاري التثبيت...
    pip install flask
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
)
echo ✅ جميع المتطلبات متوفرة

echo.
echo [3/4] بدء تشغيل النظام...
echo 🚀 جاري تشغيل الخادم...
echo.
echo ========================================
echo 🌐 الرابط: http://localhost:5000
echo 👤 المستخدم: admin  
echo 🔑 كلمة المرور: admin123
echo ========================================
echo 💡 اضغط Ctrl+C لإيقاف النظام
echo ========================================
echo.

echo [4/4] تشغيل التطبيق...

REM محاولة تشغيل الملفات بالترتيب
if exist "simple_run.py" (
    echo 📱 تشغيل النسخة البسيطة...
    python simple_run.py
) else if exist "run_final.py" (
    echo 📱 تشغيل النسخة النهائية...
    python run_final.py
) else if exist "test_app.py" (
    echo 📱 تشغيل نسخة الاختبار...
    python test_app.py
) else (
    echo ❌ لم يتم العثور على ملفات التشغيل
    echo 💡 تأكد من وجود الملفات في نفس المجلد
    pause
    exit /b 1
)

echo.
echo 👋 تم إيقاف النظام
pause
