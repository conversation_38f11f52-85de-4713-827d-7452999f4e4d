{% extends "base.html" %}

{% block title %}{{ service.service_number }} - تفاصيل الخدمة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-tools"></i>
        خدمة رقم: {{ service.service_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('services.edit', id=service.id) }}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i>
                تعديل
            </a>
            {% if not invoice %}
            <button type="button" class="btn btn-success" onclick="createInvoice({{ service.id }})">
                <i class="bi bi-receipt"></i>
                إنشاء فاتورة
            </button>
            {% endif %}
            <button type="button" class="btn btn-info" onclick="printService()">
                <i class="bi bi-printer"></i>
                طباعة
            </button>
        </div>
        <a href="{{ url_for('services.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للخدمات
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات الخدمة الأساسية -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    تفاصيل الخدمة
                </h5>
                <span class="badge status-badge 
                    {% if service.status == 'pending' %}bg-warning text-dark
                    {% elif service.status == 'in_progress' %}bg-info
                    {% elif service.status == 'completed' %}bg-success
                    {% elif service.status == 'cancelled' %}bg-danger
                    {% else %}bg-secondary{% endif %}">
                    {% if service.status == 'pending' %}معلق
                    {% elif service.status == 'in_progress' %}قيد التنفيذ
                    {% elif service.status == 'completed' %}مكتمل
                    {% elif service.status == 'cancelled' %}ملغي
                    {% else %}{{ service.status }}{% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الخدمة:</strong></td>
                                <td>{{ service.service_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>العميل:</strong></td>
                                <td>
                                    <a href="{{ url_for('customers.view', id=service.customer.id) }}" class="text-decoration-none">
                                        {{ service.customer.name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>المركبة:</strong></td>
                                <td>
                                    <a href="{{ url_for('vehicles.view', id=service.vehicle.id) }}" class="text-decoration-none">
                                        {{ service.vehicle.make }} {{ service.vehicle.model }}
                                    </a>
                                    <br><small class="text-muted">{{ service.vehicle.plate_number }}</small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>نوع الخدمة:</strong></td>
                                <td>{{ service.service_type or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>الأولوية:</strong></td>
                                <td>
                                    <span class="badge 
                                        {% if service.priority == 'urgent' %}priority-high
                                        {% elif service.priority == 'high' %}priority-high
                                        {% elif service.priority == 'medium' %}priority-medium
                                        {% else %}priority-low{% endif %}">
                                        {% if service.priority == 'urgent' %}عاجل
                                        {% elif service.priority == 'high' %}عالي
                                        {% elif service.priority == 'medium' %}متوسط
                                        {% else %}منخفض{% endif %}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>تاريخ الطلب:</strong></td>
                                <td>{{ service.requested_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% if service.started_date %}
                            <tr>
                                <td><strong>تاريخ البدء:</strong></td>
                                <td>{{ service.started_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endif %}
                            {% if service.completed_date %}
                            <tr>
                                <td><strong>تاريخ الإكمال:</strong></td>
                                <td>{{ service.completed_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>الفني المسؤول:</strong></td>
                                <td>{{ service.technician_name or 'غير محدد' }}</td>
                            </tr>
                            {% if service.warranty_period %}
                            <tr>
                                <td><strong>فترة الضمان:</strong></td>
                                <td>{{ service.warranty_period }} يوم</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <div class="mt-3">
                    <h6><strong>وصف الخدمة:</strong></h6>
                    <p class="text-muted">{{ service.description }}</p>
                </div>

                {% if service.technician_notes %}
                <div class="mt-3">
                    <h6><strong>ملاحظات الفني:</strong></h6>
                    <p class="text-muted">{{ service.technician_notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- قطع الغيار المستخدمة -->
        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-box-seam"></i>
                    قطع الغيار المستخدمة
                </h5>
                {% if service.status != 'completed' %}
                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addPartModal">
                    <i class="bi bi-plus"></i>
                    إضافة قطعة
                </button>
                {% endif %}
            </div>
            <div class="card-body">
                {% if service_parts %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>القطعة</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>الإجمالي</th>
                                {% if service.status != 'completed' %}
                                <th>الإجراءات</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for part in service_parts %}
                            <tr>
                                <td>
                                    <strong>{{ part.part.name }}</strong>
                                    <br><small class="text-muted">{{ part.part.part_number }}</small>
                                </td>
                                <td>{{ part.quantity }} {{ part.part.unit }}</td>
                                <td>{{ "%.2f"|format(part.unit_price) }} ر.س</td>
                                <td>{{ "%.2f"|format(part.total_price) }} ر.س</td>
                                {% if service.status != 'completed' %}
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="removePart({{ part.id }})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-active">
                                <th colspan="3">إجمالي قطع الغيار:</th>
                                <th>{{ "%.2f"|format(service.parts_cost) }} ر.س</th>
                                {% if service.status != 'completed' %}
                                <th></th>
                                {% endif %}
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-box-seam display-4 text-muted"></i>
                    <h6 class="mt-2">لم يتم استخدام قطع غيار</h6>
                    {% if service.status != 'completed' %}
                    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addPartModal">
                        <i class="bi bi-plus"></i>
                        إضافة قطعة غيار
                    </button>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الملخص المالي والفاتورة -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calculator"></i>
                    الملخص المالي
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td>تكلفة العمالة:</td>
                        <td class="text-end">{{ "%.2f"|format(service.labor_cost) }} ر.س</td>
                    </tr>
                    <tr>
                        <td>تكلفة قطع الغيار:</td>
                        <td class="text-end">{{ "%.2f"|format(service.parts_cost) }} ر.س</td>
                    </tr>
                    <tr class="table-active">
                        <td><strong>الإجمالي:</strong></td>
                        <td class="text-end"><strong>{{ "%.2f"|format(service.total_cost) }} ر.س</strong></td>
                    </tr>
                </table>
            </div>
        </div>

        {% if invoice %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-receipt"></i>
                    معلومات الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td>رقم الفاتورة:</td>
                        <td><strong>{{ invoice.invoice_number }}</strong></td>
                    </tr>
                    <tr>
                        <td>تاريخ الفاتورة:</td>
                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td>المبلغ الإجمالي:</td>
                        <td><strong>{{ "%.2f"|format(invoice.total_amount) }} ر.س</strong></td>
                    </tr>
                    <tr>
                        <td>المبلغ المدفوع:</td>
                        <td>{{ "%.2f"|format(invoice.paid_amount) }} ر.س</td>
                    </tr>
                    <tr>
                        <td>المبلغ المتبقي:</td>
                        <td>{{ "%.2f"|format(invoice.remaining_amount) }} ر.س</td>
                    </tr>
                    <tr>
                        <td>حالة الدفع:</td>
                        <td>
                            <span class="badge 
                                {% if invoice.payment_status == 'paid' %}bg-success
                                {% elif invoice.payment_status == 'partial' %}bg-warning
                                {% else %}bg-danger{% endif %}">
                                {% if invoice.payment_status == 'paid' %}مدفوع
                                {% elif invoice.payment_status == 'partial' %}مدفوع جزئياً
                                {% else %}غير مدفوع{% endif %}
                            </span>
                        </td>
                    </tr>
                </table>
                
                <div class="d-grid gap-2 mt-3">
                    <button type="button" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i>
                        عرض الفاتورة
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-printer"></i>
                        طباعة الفاتورة
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if service.status == 'pending' %}
                    <button type="button" class="btn btn-info btn-sm" onclick="updateStatus('in_progress')">
                        <i class="bi bi-play"></i>
                        بدء العمل
                    </button>
                    {% elif service.status == 'in_progress' %}
                    <button type="button" class="btn btn-success btn-sm" onclick="updateStatus('completed')">
                        <i class="bi bi-check-circle"></i>
                        إكمال الخدمة
                    </button>
                    {% endif %}
                    
                    {% if service.status != 'cancelled' %}
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="updateStatus('cancelled')">
                        <i class="bi bi-x-circle"></i>
                        إلغاء الخدمة
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة قطعة غيار -->
<div class="modal fade" id="addPartModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة قطعة غيار</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('services.add_part', id=service.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="part_id" class="form-label">قطعة الغيار</label>
                        <select class="form-select" id="part_id" name="part_id" required>
                            <option value="">اختر قطعة الغيار</option>
                            <!-- سيتم تحديثها عبر JavaScript -->
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">الكمية</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" value="1" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="unit_price" class="form-label">سعر الوحدة</label>
                        <input type="number" class="form-control" id="unit_price" name="unit_price" step="0.01" min="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateStatus(newStatus) {
    if (confirm('هل أنت متأكد من تغيير حالة الخدمة؟')) {
        // يمكن إضافة AJAX request هنا
        window.location.href = `{{ url_for('services.edit', id=service.id) }}`;
    }
}

function createInvoice(serviceId) {
    if (confirm('هل تريد إنشاء فاتورة لهذه الخدمة؟')) {
        fetch(`/services/${serviceId}/create_invoice`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('حدث خطأ في إنشاء الفاتورة');
            }
        });
    }
}

function removePart(partId) {
    if (confirm('هل أنت متأكد من إزالة هذه القطعة؟')) {
        fetch(`{{ url_for('services.remove_part', id=service.id, part_id=0) }}`.replace('0', partId), {
            method: 'POST'
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('حدث خطأ في إزالة القطعة');
            }
        });
    }
}

function printService() {
    window.print();
}

// تحميل قطع الغيار عند فتح المودال
document.getElementById('addPartModal').addEventListener('show.bs.modal', function() {
    fetch('/inventory/api/search?q=')
        .then(response => response.json())
        .then(parts => {
            const select = document.getElementById('part_id');
            select.innerHTML = '<option value="">اختر قطعة الغيار</option>';
            
            parts.forEach(part => {
                const option = document.createElement('option');
                option.value = part.id;
                option.textContent = `${part.name} - ${part.part_number} (${part.quantity_in_stock} متاح)`;
                option.dataset.price = part.selling_price;
                select.appendChild(option);
            });
        });
});

// تحديث السعر تلقائياً عند اختيار القطعة
document.getElementById('part_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.dataset.price) {
        document.getElementById('unit_price').value = selectedOption.dataset.price;
    }
});
</script>
{% endblock %}
