from . import db
from datetime import datetime

class Service(db.Model):
    """نموذج الخدمات والصيانة"""
    __tablename__ = 'services'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.<PERSON>Key('customers.id'), nullable=False)
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicles.id'), nullable=False)
    
    # تفاصيل الخدمة
    service_number = db.Column(db.String(20), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=False)  # وصف العطل/الخدمة
    service_type = db.Column(db.String(50))  # نوع الخدمة
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent
    
    # حالة الخدمة
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    
    # التواريخ
    requested_date = db.Column(db.DateTime, default=datetime.utcnow)
    started_date = db.Column(db.DateTime)
    completed_date = db.Column(db.DateTime)
    delivery_date = db.Column(db.DateTime)
    
    # الفني المسؤول
    technician_name = db.Column(db.String(100))
    technician_notes = db.Column(db.Text)
    
    # التكاليف
    labor_cost = db.Column(db.Float, default=0.0)  # تكلفة العمالة
    parts_cost = db.Column(db.Float, default=0.0)  # تكلفة قطع الغيار
    total_cost = db.Column(db.Float, default=0.0)  # التكلفة الإجمالية
    
    # معلومات إضافية
    warranty_period = db.Column(db.Integer)  # فترة الضمان بالأيام
    images_before = db.Column(db.Text)  # صور قبل الصيانة
    images_after = db.Column(db.Text)  # صور بعد الصيانة
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    service_parts = db.relationship('ServicePart', backref='service', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Service {self.service_number}>'
    
    def calculate_total_cost(self):
        """حساب التكلفة الإجمالية"""
        parts_total = sum([sp.total_price for sp in self.service_parts])
        self.parts_cost = parts_total
        self.total_cost = self.labor_cost + self.parts_cost
        return self.total_cost
    
    def to_dict(self):
        return {
            'id': self.id,
            'service_number': self.service_number,
            'customer_id': self.customer_id,
            'vehicle_id': self.vehicle_id,
            'description': self.description,
            'service_type': self.service_type,
            'priority': self.priority,
            'status': self.status,
            'requested_date': self.requested_date.isoformat() if self.requested_date else None,
            'completed_date': self.completed_date.isoformat() if self.completed_date else None,
            'technician_name': self.technician_name,
            'labor_cost': self.labor_cost,
            'parts_cost': self.parts_cost,
            'total_cost': self.total_cost
        }


class ServicePart(db.Model):
    """نموذج قطع الغيار المستخدمة في الخدمة"""
    __tablename__ = 'service_parts'
    
    id = db.Column(db.Integer, primary_key=True)
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'), nullable=False)
    part_id = db.Column(db.Integer, db.ForeignKey('parts.id'), nullable=False)
    
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<ServicePart {self.part.name} x{self.quantity}>'
