from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from models import db
from models.customer import Customer
from models.vehicle import Vehicle
from models.service import Service
import json

vehicles_bp = Blueprint('vehicles', __name__)

@vehicles_bp.route('/')
@login_required
def index():
    """قائمة المركبات"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Vehicle.query.join(Customer)
    
    if search:
        query = query.filter(
            Vehicle.make.contains(search) |
            Vehicle.model.contains(search) |
            Vehicle.plate_number.contains(search) |
            Customer.name.contains(search)
        )
    
    vehicles = query.order_by(Vehicle.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('vehicles/index.html', vehicles=vehicles, search=search)

@vehicles_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة مركبة جديدة"""
    if request.method == 'POST':
        customer_id = request.form.get('customer_id')
        make = request.form.get('make')
        model = request.form.get('model')
        year = request.form.get('year')
        color = request.form.get('color')
        plate_number = request.form.get('plate_number')
        chassis_number = request.form.get('chassis_number')
        engine_number = request.form.get('engine_number')
        mileage = request.form.get('mileage')
        fuel_type = request.form.get('fuel_type')
        transmission = request.form.get('transmission')
        notes = request.form.get('notes')
        
        # التحقق من البيانات المطلوبة
        if not customer_id or not make or not model or not plate_number:
            flash('العميل والماركة والموديل ورقم اللوحة مطلوبة', 'error')
            return render_template('vehicles/add.html')
        
        # التحقق من عدم تكرار رقم اللوحة
        if Vehicle.query.filter_by(plate_number=plate_number).first():
            flash('رقم اللوحة موجود بالفعل', 'error')
            return render_template('vehicles/add.html')
        
        # التحقق من وجود العميل
        customer = Customer.query.get(customer_id)
        if not customer:
            flash('العميل غير موجود', 'error')
            return render_template('vehicles/add.html')
        
        # إنشاء المركبة الجديدة
        vehicle = Vehicle(
            customer_id=customer_id,
            make=make,
            model=model,
            year=int(year) if year else None,
            color=color,
            plate_number=plate_number,
            chassis_number=chassis_number,
            engine_number=engine_number,
            mileage=int(mileage) if mileage else None,
            fuel_type=fuel_type,
            transmission=transmission,
            notes=notes
        )
        
        db.session.add(vehicle)
        db.session.commit()
        flash('تم إضافة المركبة بنجاح', 'success')
        return redirect(url_for('vehicles.view', id=vehicle.id))
    
    # جلب العملاء للقائمة المنسدلة
    customers = Customer.query.order_by(Customer.name).all()
    return render_template('vehicles/add.html', customers=customers)

@vehicles_bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل المركبة"""
    vehicle = Vehicle.query.get_or_404(id)
    
    # جلب تاريخ الخدمات
    services = Service.query.filter_by(vehicle_id=id).order_by(Service.created_at.desc()).all()
    
    return render_template('vehicles/view.html', vehicle=vehicle, services=services)

@vehicles_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل بيانات المركبة"""
    vehicle = Vehicle.query.get_or_404(id)
    
    if request.method == 'POST':
        customer_id = request.form.get('customer_id')
        make = request.form.get('make')
        model = request.form.get('model')
        year = request.form.get('year')
        color = request.form.get('color')
        plate_number = request.form.get('plate_number')
        chassis_number = request.form.get('chassis_number')
        engine_number = request.form.get('engine_number')
        mileage = request.form.get('mileage')
        fuel_type = request.form.get('fuel_type')
        transmission = request.form.get('transmission')
        notes = request.form.get('notes')
        
        # التحقق من البيانات المطلوبة
        if not customer_id or not make or not model or not plate_number:
            flash('العميل والماركة والموديل ورقم اللوحة مطلوبة', 'error')
            customers = Customer.query.order_by(Customer.name).all()
            return render_template('vehicles/edit.html', vehicle=vehicle, customers=customers)
        
        # التحقق من عدم تكرار رقم اللوحة (باستثناء المركبة الحالية)
        existing_vehicle = Vehicle.query.filter_by(plate_number=plate_number).first()
        if existing_vehicle and existing_vehicle.id != vehicle.id:
            flash('رقم اللوحة موجود بالفعل', 'error')
            customers = Customer.query.order_by(Customer.name).all()
            return render_template('vehicles/edit.html', vehicle=vehicle, customers=customers)
        
        # تحديث البيانات
        vehicle.customer_id = customer_id
        vehicle.make = make
        vehicle.model = model
        vehicle.year = int(year) if year else None
        vehicle.color = color
        vehicle.plate_number = plate_number
        vehicle.chassis_number = chassis_number
        vehicle.engine_number = engine_number
        vehicle.mileage = int(mileage) if mileage else None
        vehicle.fuel_type = fuel_type
        vehicle.transmission = transmission
        vehicle.notes = notes
        
        db.session.commit()
        flash('تم تحديث بيانات المركبة بنجاح', 'success')
        return redirect(url_for('vehicles.view', id=vehicle.id))
    
    customers = Customer.query.order_by(Customer.name).all()
    return render_template('vehicles/edit.html', vehicle=vehicle, customers=customers)

@vehicles_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف المركبة"""
    vehicle = Vehicle.query.get_or_404(id)
    
    # التحقق من وجود خدمات مرتبطة
    if vehicle.services:
        flash('لا يمكن حذف المركبة لوجود خدمات مرتبطة بها', 'error')
        return redirect(url_for('vehicles.view', id=vehicle.id))
    
    db.session.delete(vehicle)
    db.session.commit()
    flash('تم حذف المركبة بنجاح', 'success')
    return redirect(url_for('vehicles.index'))

@vehicles_bp.route('/api/by_customer/<int:customer_id>')
@login_required
def api_by_customer(customer_id):
    """جلب مركبات العميل عبر API"""
    vehicles = Vehicle.query.filter_by(customer_id=customer_id).all()
    
    results = []
    for vehicle in vehicles:
        results.append({
            'id': vehicle.id,
            'make': vehicle.make,
            'model': vehicle.model,
            'year': vehicle.year,
            'plate_number': vehicle.plate_number,
            'display_name': f"{vehicle.make} {vehicle.model} - {vehicle.plate_number}"
        })
    
    return jsonify(results)
