from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required
from models import db
from models.customer import Customer
from models.vehicle import Vehicle
from models.service import Service
from models.inventory import Part
from models.invoice import Invoice, Payment
from sqlalchemy import func, extract
from datetime import datetime, timedelta
import calendar

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@login_required
def index():
    """صفحة التقارير الرئيسية"""
    return render_template('reports/index.html')

@reports_bp.route('/financial')
@login_required
def financial():
    """التقارير المالية"""
    # فترة التقرير
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date or not end_date:
        # افتراضياً: آخر 30 يوم
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # الإيرادات
    revenue_query = db.session.query(func.sum(Payment.amount)).filter(
        Payment.payment_date >= start_date,
        Payment.payment_date <= end_date
    ).scalar() or 0
    
    # عدد الفواتير المدفوعة
    paid_invoices = Invoice.query.filter(
        Invoice.invoice_date >= start_date,
        Invoice.invoice_date <= end_date,
        Invoice.payment_status.in_(['paid', 'partial'])
    ).count()
    
    # الفواتير المعلقة
    pending_invoices = Invoice.query.filter(
        Invoice.payment_status == 'pending'
    ).count()
    
    # إجمالي المبالغ المعلقة
    pending_amount = db.session.query(func.sum(Invoice.remaining_amount)).filter(
        Invoice.payment_status.in_(['pending', 'partial'])
    ).scalar() or 0
    
    # الإيرادات الشهرية (آخر 12 شهر)
    monthly_revenue = []
    for i in range(12):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
        
        revenue = db.session.query(func.sum(Payment.amount)).filter(
            Payment.payment_date >= month_start.date(),
            Payment.payment_date <= month_end.date()
        ).scalar() or 0
        
        monthly_revenue.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            'revenue': revenue
        })
    
    monthly_revenue.reverse()
    
    return render_template('reports/financial.html',
                         start_date=start_date,
                         end_date=end_date,
                         revenue=revenue_query,
                         paid_invoices=paid_invoices,
                         pending_invoices=pending_invoices,
                         pending_amount=pending_amount,
                         monthly_revenue=monthly_revenue)

@reports_bp.route('/services')
@login_required
def services():
    """تقارير الخدمات"""
    # إحصائيات الخدمات حسب الحالة
    service_stats = db.session.query(
        Service.status,
        func.count(Service.id).label('count')
    ).group_by(Service.status).all()
    
    # أكثر أنواع الخدمات طلباً
    service_types = db.session.query(
        Service.service_type,
        func.count(Service.id).label('count')
    ).filter(Service.service_type.isnot(None)).group_by(Service.service_type).order_by(func.count(Service.id).desc()).limit(10).all()
    
    # أداء الفنيين
    technician_performance = db.session.query(
        Service.technician_name,
        func.count(Service.id).label('total_services'),
        func.count(Service.id).filter(Service.status == 'completed').label('completed_services'),
        func.avg(Service.total_cost).label('avg_cost')
    ).filter(Service.technician_name.isnot(None)).group_by(Service.technician_name).all()
    
    # الخدمات الشهرية
    monthly_services = []
    for i in range(6):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
        
        count = Service.query.filter(
            Service.requested_date >= month_start,
            Service.requested_date <= month_end
        ).count()
        
        monthly_services.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            'count': count
        })
    
    monthly_services.reverse()
    
    return render_template('reports/services.html',
                         service_stats=service_stats,
                         service_types=service_types,
                         technician_performance=technician_performance,
                         monthly_services=monthly_services)

@reports_bp.route('/inventory')
@login_required
def inventory():
    """تقارير المخزون"""
    # قطع الغيار منخفضة المخزون
    low_stock_parts = Part.query.filter(
        Part.quantity_in_stock <= Part.minimum_stock,
        Part.is_active == True
    ).order_by(Part.quantity_in_stock).all()
    
    # أكثر قطع الغيار استخداماً
    # (هذا يتطلب جدول ServicePart)
    
    # إحصائيات المخزون حسب الفئة
    inventory_by_category = db.session.query(
        Part.category,
        func.count(Part.id).label('parts_count'),
        func.sum(Part.quantity_in_stock).label('total_quantity'),
        func.sum(Part.quantity_in_stock * Part.cost_price).label('total_value')
    ).filter(Part.is_active == True).group_by(Part.category).all()
    
    # إجمالي قيمة المخزون
    total_inventory_value = db.session.query(
        func.sum(Part.quantity_in_stock * Part.cost_price)
    ).filter(Part.is_active == True).scalar() or 0
    
    return render_template('reports/inventory.html',
                         low_stock_parts=low_stock_parts,
                         inventory_by_category=inventory_by_category,
                         total_inventory_value=total_inventory_value)

@reports_bp.route('/customers')
@login_required
def customers():
    """تقارير العملاء"""
    # أكثر العملاء نشاطاً
    top_customers = db.session.query(
        Customer.id,
        Customer.name,
        Customer.phone,
        func.count(Service.id).label('services_count'),
        func.sum(Service.total_cost).label('total_spent')
    ).join(Service).group_by(Customer.id).order_by(func.sum(Service.total_cost).desc()).limit(10).all()
    
    # العملاء الجدد (آخر 30 يوم)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    new_customers = Customer.query.filter(
        Customer.created_at >= thirty_days_ago
    ).count()
    
    # إجمالي العملاء
    total_customers = Customer.query.count()
    
    # العملاء حسب الشهر
    monthly_customers = []
    for i in range(6):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
        
        count = Customer.query.filter(
            Customer.created_at >= month_start,
            Customer.created_at <= month_end
        ).count()
        
        monthly_customers.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            'count': count
        })
    
    monthly_customers.reverse()
    
    return render_template('reports/customers.html',
                         top_customers=top_customers,
                         new_customers=new_customers,
                         total_customers=total_customers,
                         monthly_customers=monthly_customers)

@reports_bp.route('/api/dashboard_stats')
@login_required
def api_dashboard_stats():
    """إحصائيات سريعة للوحة التحكم"""
    today = datetime.now().date()
    this_month_start = datetime.now().replace(day=1).date()
    
    stats = {
        'today_services': Service.query.filter(
            func.date(Service.requested_date) == today
        ).count(),
        'this_month_revenue': db.session.query(func.sum(Payment.amount)).filter(
            Payment.payment_date >= this_month_start
        ).scalar() or 0,
        'pending_services': Service.query.filter_by(status='pending').count(),
        'low_stock_alerts': Part.query.filter(
            Part.quantity_in_stock <= Part.minimum_stock,
            Part.is_active == True
        ).count()
    }
    
    return jsonify(stats)
