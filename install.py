#!/usr/bin/env python3
"""
أداة تثبيت نظام إدارة مركز صيانة السيارات
"""

import os
import sys
import subprocess
import platform

def print_header():
    """طباعة رأس التثبيت"""
    print("🚗 مرحباً بك في أداة تثبيت نظام إدارة مركز صيانة السيارات")
    print("=" * 70)
    print("هذه الأداة ستقوم بتثبيت وإعداد النظام تلقائياً")
    print("=" * 70)

def check_python():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_pip():
    """فحص وجود pip"""
    print("📦 فحص pip...")
    
    try:
        import pip
        print("✅ pip متوفر")
        return True
    except ImportError:
        print("❌ pip غير متوفر")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("📥 تثبيت المتطلبات...")
    
    if not os.path.exists('requirements.txt'):
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        # تثبيت المتطلبات
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            print("❌ فشل في تثبيت المتطلبات")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات...")
    
    directories = [
        'instance',
        'logs',
        'backups',
        'static/uploads',
        'static/css',
        'static/js'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ تم إنشاء مجلد: {directory}")
        else:
            print(f"📁 مجلد موجود: {directory}")
    
    return True

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    try:
        # استيراد التطبيق وإنشاء قاعدة البيانات
        if os.path.exists('app_complete.py'):
            from app_complete import app, db
        else:
            from test_app import app, db
        
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def create_admin_user():
    """إنشاء المستخدم الافتراضي"""
    print("👤 إنشاء المستخدم الافتراضي...")
    
    try:
        if os.path.exists('app_complete.py'):
            from app_complete import app, db, User
        else:
            from test_app import app, db, User
        
        from werkzeug.security import generate_password_hash
        
        with app.app_context():
            # التحقق من وجود المستخدم
            if User.query.filter_by(username='admin').first():
                print("👤 المستخدم الافتراضي موجود بالفعل")
                return True
            
            # إنشاء المستخدم
            admin_user = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                full_name='مدير النظام',
                role='admin'
            )
            
            # إضافة البريد الإلكتروني إذا كان متاحاً
            if hasattr(admin_user, 'email'):
                admin_user.email = '<EMAIL>'
            
            db.session.add(admin_user)
            db.session.commit()
            
            print("✅ تم إنشاء المستخدم الافتراضي")
            print("   المستخدم: admin")
            print("   كلمة المرور: admin123")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    print("📊 إنشاء بيانات تجريبية...")
    
    try:
        if os.path.exists('app_complete.py'):
            from app_complete import app, db, Customer, Vehicle, Part
        else:
            print("⚠️ البيانات التجريبية متاحة فقط مع التطبيق الكامل")
            return True
        
        with app.app_context():
            # التحقق من وجود بيانات
            if Customer.query.first():
                print("📊 البيانات التجريبية موجودة بالفعل")
                return True
            
            # إنشاء عميل تجريبي
            customer = Customer(
                name='أحمد محمد السعودي',
                phone='0501234567',
                email='<EMAIL>',
                address='الرياض، المملكة العربية السعودية'
            )
            db.session.add(customer)
            db.session.flush()  # للحصول على ID
            
            # إنشاء مركبة تجريبية
            vehicle = Vehicle(
                customer_id=customer.id,
                make='تويوتا',
                model='كامري',
                year=2020,
                color='أبيض',
                plate_number='أ ب ج 123',
                chassis_number='JT123456789',
                engine_number='ENG123456',
                mileage=50000,
                fuel_type='بنزين',
                transmission='أوتوماتيك'
            )
            db.session.add(vehicle)
            
            # إنشاء قطع غيار تجريبية
            parts_data = [
                {
                    'name': 'زيت المحرك 5W-30',
                    'part_number': 'OIL001',
                    'category': 'زيوت ومواد تشحيم',
                    'brand': 'موبيل',
                    'unit': 'لتر',
                    'cost_price': 25.0,
                    'selling_price': 35.0,
                    'quantity_in_stock': 50,
                    'minimum_stock': 10,
                    'supplier_name': 'شركة قطع الغيار المتحدة',
                    'supplier_contact': '0112345678'
                },
                {
                    'name': 'فلتر الهواء',
                    'part_number': 'AIR001',
                    'category': 'فلاتر',
                    'brand': 'مان',
                    'unit': 'قطعة',
                    'cost_price': 15.0,
                    'selling_price': 25.0,
                    'quantity_in_stock': 20,
                    'minimum_stock': 5,
                    'supplier_name': 'مؤسسة الفلاتر الذهبية',
                    'supplier_contact': '0112345679'
                },
                {
                    'name': 'تيل الفرامل الأمامي',
                    'part_number': 'BRK001',
                    'category': 'فرامل',
                    'brand': 'بريمبو',
                    'unit': 'طقم',
                    'cost_price': 80.0,
                    'selling_price': 120.0,
                    'quantity_in_stock': 8,
                    'minimum_stock': 3,
                    'supplier_name': 'مركز قطع الفرامل',
                    'supplier_contact': '0112345680'
                },
                {
                    'name': 'بطارية السيارة 12V',
                    'part_number': 'BAT001',
                    'category': 'كهرباء',
                    'brand': 'فارتا',
                    'unit': 'قطعة',
                    'cost_price': 200.0,
                    'selling_price': 280.0,
                    'quantity_in_stock': 5,
                    'minimum_stock': 2,
                    'supplier_name': 'مؤسسة البطاريات الحديثة',
                    'supplier_contact': '0112345681'
                }
            ]
            
            for part_data in parts_data:
                part = Part(**part_data)
                db.session.add(part)
            
            db.session.commit()
            print("✅ تم إنشاء البيانات التجريبية")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def create_startup_script():
    """إنشاء ملف تشغيل سهل"""
    print("🚀 إنشاء ملف التشغيل...")
    
    # ملف تشغيل لـ Windows
    if platform.system() == 'Windows':
        with open('start.bat', 'w', encoding='utf-8') as f:
            f.write('@echo off\n')
            f.write('echo 🚗 نظام إدارة مركز صيانة السيارات\n')
            f.write('echo =====================================\n')
            f.write('python run_final.py\n')
            f.write('pause\n')
        print("✅ تم إنشاء ملف start.bat")
    
    # ملف تشغيل لـ Linux/Mac
    with open('start.sh', 'w') as f:
        f.write('#!/bin/bash\n')
        f.write('echo "🚗 نظام إدارة مركز صيانة السيارات"\n')
        f.write('echo "====================================="\n')
        f.write('python3 run_final.py\n')
    
    # جعل الملف قابل للتنفيذ
    if platform.system() != 'Windows':
        os.chmod('start.sh', 0o755)
        print("✅ تم إنشاء ملف start.sh")
    
    return True

def show_completion_message():
    """عرض رسالة الإكمال"""
    print("\n" + "=" * 70)
    print("🎉 تم تثبيت النظام بنجاح!")
    print("=" * 70)
    print("📋 معلومات مهمة:")
    print("   🌐 الرابط: http://localhost:5000")
    print("   👤 المستخدم: admin")
    print("   🔑 كلمة المرور: admin123")
    print()
    print("🚀 طرق التشغيل:")
    print("   1. python run_final.py")
    if platform.system() == 'Windows':
        print("   2. start.bat")
    else:
        print("   2. ./start.sh")
    print()
    print("📚 ملفات مهمة:")
    print("   - README.md: دليل المستخدم الكامل")
    print("   - QUICKSTART.md: دليل البدء السريع")
    print("   - FINAL_INSTRUCTIONS.md: تعليمات التشغيل")
    print("=" * 70)

def main():
    """الدالة الرئيسية للتثبيت"""
    print_header()
    
    # فحص المتطلبات الأساسية
    if not check_python():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # خطوات التثبيت
    steps = [
        ("تثبيت المتطلبات", install_requirements),
        ("إنشاء المجلدات", create_directories),
        ("إعداد قاعدة البيانات", setup_database),
        ("إنشاء المستخدم الافتراضي", create_admin_user),
        ("إنشاء البيانات التجريبية", create_sample_data),
        ("إنشاء ملف التشغيل", create_startup_script)
    ]
    
    print("\n🔄 بدء عملية التثبيت...")
    print("-" * 40)
    
    for step_name, step_function in steps:
        print(f"\n📌 {step_name}...")
        if not step_function():
            print(f"❌ فشل في: {step_name}")
            sys.exit(1)
    
    show_completion_message()

if __name__ == '__main__':
    main()
