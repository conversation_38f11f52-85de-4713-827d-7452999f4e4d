{% extends "base.html" %}

{% block title %}تعديل {{ customer.name }} - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-pencil"></i>
        تعديل بيانات العميل
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('customers.view', id=customer.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة لصفحة العميل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">تعديل بيانات العميل</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ customer.name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ customer.phone }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ customer.email or '' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   value="{{ customer.national_id or '' }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3">{{ customer.address or '' }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ customer.notes or '' }}</textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            حفظ التغييرات
                        </button>
                        <a href="{{ url_for('customers.view', id=customer.id) }}" class="btn btn-secondary">
                            <i class="bi bi-x-lg"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <strong>تاريخ التسجيل:</strong><br>
                        <small class="text-muted">{{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </li>
                    <li class="mb-2">
                        <strong>آخر تحديث:</strong><br>
                        <small class="text-muted">{{ customer.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </li>
                    <li class="mb-2">
                        <strong>عدد المركبات:</strong><br>
                        <span class="badge bg-primary">{{ customer.vehicles|length }}</span>
                    </li>
                    <li class="mb-2">
                        <strong>عدد الخدمات:</strong><br>
                        <span class="badge bg-success">{{ customer.services|length }}</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                    تنبيه
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    <i class="bi bi-info-circle"></i>
                    تأكد من صحة البيانات قبل الحفظ. رقم الهاتف يجب أن يكون فريد.
                </p>
                {% if customer.vehicles|length > 0 or customer.services|length > 0 %}
                <div class="alert alert-warning alert-sm">
                    <small>
                        <i class="bi bi-exclamation-triangle"></i>
                        هذا العميل لديه مركبات وخدمات مرتبطة. لا يمكن حذفه.
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
