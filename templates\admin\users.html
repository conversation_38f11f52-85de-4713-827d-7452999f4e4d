{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-people"></i>
        إدارة المستخدمين والصلاحيات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="bi bi-person-plus"></i>
                مستخدم جديد
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="exportUsers()">
                <i class="bi bi-download"></i>
                تصدير
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-funnel"></i>
                فلترة
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?role=all">جميع المستخدمين</a></li>
                <li><a class="dropdown-item" href="?role=admin">المديرين</a></li>
                <li><a class="dropdown-item" href="?role=manager">المشرفين</a></li>
                <li><a class="dropdown-item" href="?role=technician">الفنيين</a></li>
                <li><a class="dropdown-item" href="?role=user">المستخدمين</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="?status=active">النشطين</a></li>
                <li><a class="dropdown-item" href="?status=inactive">غير النشطين</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- إحصائيات المستخدمين -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card info">
            <div class="card-body text-center">
                <h3 class="text-white">{{ total_users or 8 }}</h3>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card success">
            <div class="card-body text-center">
                <h3 class="text-white">{{ active_users or 6 }}</h3>
                <p class="mb-0">مستخدمين نشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card warning">
            <div class="card-body text-center">
                <h3 class="text-white">{{ online_users or 3 }}</h3>
                <p class="mb-0">متصلين الآن</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card danger">
            <div class="card-body text-center">
                <h3 class="text-white">{{ new_users_today or 1 }}</h3>
                <p class="mb-0">مستخدمين جدد اليوم</p>
            </div>
        </div>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-list-ul"></i>
            قائمة المستخدمين
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>المستخدم</th>
                        <th>الدور</th>
                        <th>الصلاحيات</th>
                        <th>آخر دخول</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- مدير النظام -->
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-md me-3">
                                    <img src="https://via.placeholder.com/40x40/007bff/ffffff?text=أ" 
                                         class="rounded-circle" alt="أحمد محمد">
                                </div>
                                <div>
                                    <strong>أحمد محمد السعودي</strong>
                                    <br><small class="text-muted">admin</small>
                                    <br><small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-danger">مدير النظام</span>
                        </td>
                        <td>
                            <div class="permissions-list">
                                <span class="badge bg-success me-1">جميع الصلاحيات</span>
                            </div>
                        </td>
                        <td>
                            <small>منذ 5 دقائق</small>
                            <br><span class="badge bg-success">متصل</span>
                        </td>
                        <td>
                            <span class="badge bg-success">نشط</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="viewUser(1)" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="editUser(1)" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="viewActivity(1)" title="النشاط">
                                    <i class="bi bi-activity"></i>
                                </button>
                            </div>
                        </td>
                    </tr>

                    <!-- مشرف -->
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-md me-3">
                                    <img src="https://via.placeholder.com/40x40/28a745/ffffff?text=س" 
                                         class="rounded-circle" alt="سعد العتيبي">
                                </div>
                                <div>
                                    <strong>سعد العتيبي</strong>
                                    <br><small class="text-muted">manager</small>
                                    <br><small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-warning">مشرف</span>
                        </td>
                        <td>
                            <div class="permissions-list">
                                <span class="badge bg-info me-1">الخدمات</span>
                                <span class="badge bg-info me-1">المخزون</span>
                                <span class="badge bg-info me-1">التقارير</span>
                            </div>
                        </td>
                        <td>
                            <small>منذ ساعة</small>
                            <br><span class="badge bg-secondary">غير متصل</span>
                        </td>
                        <td>
                            <span class="badge bg-success">نشط</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="viewUser(2)" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="editUser(2)" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="viewActivity(2)" title="النشاط">
                                    <i class="bi bi-activity"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deactivateUser(2)" title="إلغاء التفعيل">
                                    <i class="bi bi-person-x"></i>
                                </button>
                            </div>
                        </td>
                    </tr>

                    <!-- فني -->
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-md me-3">
                                    <img src="https://via.placeholder.com/40x40/17a2b8/ffffff?text=م" 
                                         class="rounded-circle" alt="محمد الأحمد">
                                </div>
                                <div>
                                    <strong>محمد الأحمد</strong>
                                    <br><small class="text-muted">technician</small>
                                    <br><small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">فني</span>
                        </td>
                        <td>
                            <div class="permissions-list">
                                <span class="badge bg-primary me-1">الخدمات</span>
                                <span class="badge bg-primary me-1">المخزون (قراءة)</span>
                            </div>
                        </td>
                        <td>
                            <small>منذ 30 دقيقة</small>
                            <br><span class="badge bg-success">متصل</span>
                        </td>
                        <td>
                            <span class="badge bg-success">نشط</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="viewUser(3)" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="editUser(3)" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="viewActivity(3)" title="النشاط">
                                    <i class="bi bi-activity"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deactivateUser(3)" title="إلغاء التفعيل">
                                    <i class="bi bi-person-x"></i>
                                </button>
                            </div>
                        </td>
                    </tr>

                    <!-- مستخدم عادي -->
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-md me-3">
                                    <img src="https://via.placeholder.com/40x40/6c757d/ffffff?text=ع" 
                                         class="rounded-circle" alt="عبدالله السعد">
                                </div>
                                <div>
                                    <strong>عبدالله السعد</strong>
                                    <br><small class="text-muted">user</small>
                                    <br><small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">مستخدم</span>
                        </td>
                        <td>
                            <div class="permissions-list">
                                <span class="badge bg-light text-dark me-1">العملاء (قراءة)</span>
                                <span class="badge bg-light text-dark me-1">المركبات (قراءة)</span>
                            </div>
                        </td>
                        <td>
                            <small>أمس</small>
                            <br><span class="badge bg-secondary">غير متصل</span>
                        </td>
                        <td>
                            <span class="badge bg-warning">معلق</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="viewUser(4)" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="editUser(4)" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="viewActivity(4)" title="النشاط">
                                    <i class="bi bi-activity"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="activateUser(4)" title="تفعيل">
                                    <i class="bi bi-person-check"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="userForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="manager">مشرف</option>
                                <option value="technician">فني</option>
                                <option value="user">مستخدم</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">القسم</label>
                            <select class="form-select" id="department" name="department">
                                <option value="">اختر القسم</option>
                                <option value="management">الإدارة</option>
                                <option value="technical">الفني</option>
                                <option value="customer_service">خدمة العملاء</option>
                                <option value="inventory">المخزون</option>
                                <option value="finance">المالية</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الصلاحيات</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_customers" name="permissions" value="customers">
                                    <label class="form-check-label" for="perm_customers">إدارة العملاء</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_vehicles" name="permissions" value="vehicles">
                                    <label class="form-check-label" for="perm_vehicles">إدارة المركبات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_services" name="permissions" value="services">
                                    <label class="form-check-label" for="perm_services">إدارة الخدمات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_inventory" name="permissions" value="inventory">
                                    <label class="form-check-label" for="perm_inventory">إدارة المخزون</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_invoices" name="permissions" value="invoices">
                                    <label class="form-check-label" for="perm_invoices">إدارة الفواتير</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_reports" name="permissions" value="reports">
                                    <label class="form-check-label" for="perm_reports">التقارير</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_users" name="permissions" value="users">
                                    <label class="form-check-label" for="perm_users">إدارة المستخدمين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_settings" name="permissions" value="settings">
                                    <label class="form-check-label" for="perm_settings">إعدادات النظام</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            مستخدم نشط
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="send_welcome_email" name="send_welcome_email" checked>
                        <label class="form-check-label" for="send_welcome_email">
                            إرسال بريد ترحيبي
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ المستخدم</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    border: none;
    border-radius: 10px;
    color: white;
}

.stats-card.info { background: linear-gradient(45deg, #17a2b8, #20c997); }
.stats-card.success { background: linear-gradient(45deg, #28a745, #20c997); }
.stats-card.warning { background: linear-gradient(45deg, #ffc107, #fd7e14); }
.stats-card.danger { background: linear-gradient(45deg, #dc3545, #e83e8c); }

.avatar-md {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.permissions-list .badge {
    font-size: 0.7rem;
    margin-bottom: 2px;
}

.table td {
    vertical-align: middle;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// وظائف إدارة المستخدمين
function viewUser(userId) {
    window.location.href = `/admin/users/${userId}`;
}

function editUser(userId) {
    window.location.href = `/admin/users/${userId}/edit`;
}

function viewActivity(userId) {
    window.location.href = `/admin/users/${userId}/activity`;
}

function deactivateUser(userId) {
    if (confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')) {
        fetch(`/admin/users/${userId}/deactivate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}

function activateUser(userId) {
    if (confirm('هل أنت متأكد من تفعيل هذا المستخدم؟')) {
        fetch(`/admin/users/${userId}/activate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}

function exportUsers() {
    window.location.href = '/admin/users/export';
}

// تحديث الصلاحيات حسب الدور
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const permissions = document.querySelectorAll('input[name="permissions"]');
    
    // إزالة جميع الصلاحيات
    permissions.forEach(perm => perm.checked = false);
    
    // تعيين الصلاحيات حسب الدور
    switch(role) {
        case 'admin':
            permissions.forEach(perm => perm.checked = true);
            break;
        case 'manager':
            ['customers', 'vehicles', 'services', 'inventory', 'invoices', 'reports'].forEach(perm => {
                const checkbox = document.getElementById(`perm_${perm}`);
                if (checkbox) checkbox.checked = true;
            });
            break;
        case 'technician':
            ['services', 'inventory'].forEach(perm => {
                const checkbox = document.getElementById(`perm_${perm}`);
                if (checkbox) checkbox.checked = true;
            });
            break;
        case 'user':
            ['customers', 'vehicles'].forEach(perm => {
                const checkbox = document.getElementById(`perm_${perm}`);
                if (checkbox) checkbox.checked = true;
            });
            break;
    }
});

// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// حفظ المستخدم الجديد
document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userData = Object.fromEntries(formData);
    
    // جمع الصلاحيات
    const permissions = [];
    document.querySelectorAll('input[name="permissions"]:checked').forEach(perm => {
        permissions.push(perm.value);
    });
    userData.permissions = permissions;
    
    // إرسال البيانات للخادم
    fetch('/admin/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ في حفظ المستخدم: ' + data.message);
        }
    });
});

// توليد اسم المستخدم من الاسم الكامل
document.getElementById('full_name').addEventListener('input', function() {
    const fullName = this.value;
    if (fullName && !document.getElementById('username').value) {
        // توليد اسم مستخدم بسيط
        const username = fullName.toLowerCase()
            .replace(/\s+/g, '')
            .replace(/[^\w]/g, '')
            .substring(0, 15);
        document.getElementById('username').value = username;
    }
});
</script>
{% endblock %>
