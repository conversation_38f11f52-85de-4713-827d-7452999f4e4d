{% extends "base.html" %}

{% block title %}تقارير الخدمات - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-tools"></i>
        تقارير الخدمات والصيانة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-success">
                <i class="bi bi-download"></i>
                تصدير PDF
            </button>
            <button type="button" class="btn btn-outline-info">
                <i class="bi bi-file-earmark-excel"></i>
                تصدير Excel
            </button>
        </div>
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للتقارير
        </a>
    </div>
</div>

<!-- فلاتر التقرير -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ end_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلق</option>
                            <option value="in_progress" {% if status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                            <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتمل</option>
                            <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="technician" class="form-label">الفني</label>
                        <select class="form-select" id="technician" name="technician">
                            <option value="">جميع الفنيين</option>
                            {% for tech in technicians %}
                            <option value="{{ tech }}" {% if technician == tech %}selected{% endif %}>{{ tech }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                            تحديث التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي الخدمات
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ total_services }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-tools fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            خدمات مكتملة
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ completed_services }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            متوسط وقت الإنجاز
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ "%.1f"|format(avg_completion_time) }} يوم
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            معدل الرضا
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ "%.1f"|format(satisfaction_rate) }}%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-star fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row">
    <!-- رسم الخدمات اليومية -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    الخدمات اليومية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyServicesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- توزيع حالات الخدمات -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    توزيع حالات الخدمات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="300" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- أداء الفنيين -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-gear"></i>
                    أداء الفنيين
                </h5>
            </div>
            <div class="card-body">
                {% if technician_performance %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الفني</th>
                                <th>عدد الخدمات</th>
                                <th>مكتملة</th>
                                <th>معدل الإنجاز</th>
                                <th>متوسط الوقت</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tech in technician_performance %}
                            <tr>
                                <td><strong>{{ tech.name }}</strong></td>
                                <td>{{ tech.total_services }}</td>
                                <td>{{ tech.completed_services }}</td>
                                <td>
                                    {% set completion_rate = (tech.completed_services / tech.total_services * 100) if tech.total_services > 0 else 0 %}
                                    <span class="badge {% if completion_rate >= 90 %}bg-success{% elif completion_rate >= 70 %}bg-warning{% else %}bg-danger{% endif %}">
                                        {{ "%.1f"|format(completion_rate) }}%
                                    </span>
                                </td>
                                <td>{{ "%.1f"|format(tech.avg_completion_time) }} يوم</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد بيانات أداء متاحة</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- أنواع الخدمات الأكثر شيوعاً -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    أنواع الخدمات الأكثر شيوعاً
                </h5>
            </div>
            <div class="card-body">
                {% if service_types %}
                <div class="list-group">
                    {% for service_type in service_types %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ service_type.type or 'خدمات عامة' }}</h6>
                            <small class="text-muted">{{ "%.1f"|format(service_type.percentage) }}% من إجمالي الخدمات</small>
                        </div>
                        <span class="badge bg-primary rounded-pill">{{ service_type.count }}</span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد بيانات متاحة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- جدول تفصيلي للخدمات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    تفاصيل الخدمات
                </h5>
            </div>
            <div class="card-body">
                {% if services %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الخدمة</th>
                                <th>العميل</th>
                                <th>المركبة</th>
                                <th>نوع الخدمة</th>
                                <th>الفني</th>
                                <th>الحالة</th>
                                <th>تاريخ الطلب</th>
                                <th>تاريخ الإكمال</th>
                                <th>مدة الإنجاز</th>
                                <th>التكلفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in services %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('services.view', id=service.id) }}" class="text-decoration-none">
                                        {{ service.service_number }}
                                    </a>
                                </td>
                                <td>{{ service.customer.name }}</td>
                                <td>{{ service.vehicle.make }} {{ service.vehicle.model }}</td>
                                <td>{{ service.service_type or 'عام' }}</td>
                                <td>{{ service.technician_name or '-' }}</td>
                                <td>
                                    <span class="badge 
                                        {% if service.status == 'completed' %}bg-success
                                        {% elif service.status == 'in_progress' %}bg-info
                                        {% elif service.status == 'pending' %}bg-warning
                                        {% else %}bg-danger{% endif %}">
                                        {% if service.status == 'completed' %}مكتمل
                                        {% elif service.status == 'in_progress' %}قيد التنفيذ
                                        {% elif service.status == 'pending' %}معلق
                                        {% else %}ملغي{% endif %}
                                    </span>
                                </td>
                                <td>{{ service.requested_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if service.completed_date %}
                                        {{ service.completed_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if service.completed_date %}
                                        {{ (service.completed_date - service.requested_date).days }} يوم
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(service.total_cost) }} ر.س</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-active">
                                <th colspan="9">الإجمالي:</th>
                                <th>{{ "%.2f"|format(services|map(attribute='total_cost')|sum) }} ر.س</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-tools display-4 text-muted"></i>
                    <h5 class="mt-3">لا توجد خدمات</h5>
                    <p class="text-muted">لا توجد خدمات مطابقة للفلاتر المحددة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم الخدمات اليومية
const dailyCtx = document.getElementById('dailyServicesChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: [
            {% for day in daily_services %}
            '{{ day.date.strftime("%m-%d") }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'عدد الخدمات',
            data: [
                {% for day in daily_services %}
                {{ day.count }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'عدد الخدمات اليومية'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم توزيع الحالات
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['مكتمل', 'قيد التنفيذ', 'معلق', 'ملغي'],
        datasets: [{
            data: [
                {{ completed_services }},
                {{ in_progress_services }},
                {{ pending_services }},
                {{ cancelled_services }}
            ],
            backgroundColor: [
                'rgba(75, 192, 192, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 206, 86, 0.8)',
                'rgba(255, 99, 132, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'توزيع حالات الخدمات'
            },
            legend: {
                position: 'bottom'
            }
        }
    }
});

// تحديث التواريخ تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (!startDate.value) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        startDate.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
    
    if (!endDate.value) {
        const today = new Date();
        endDate.value = today.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
