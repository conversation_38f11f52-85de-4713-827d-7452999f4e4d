#!/usr/bin/env python3
"""
إعداد نظام إدارة مركز صيانة السيارات
"""

import os
import sys
from pathlib import Path

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'instance',
        'static/uploads',
        'static/css',
        'static/js',
        'templates/auth',
        'templates/customers',
        'templates/vehicles',
        'templates/services',
        'templates/inventory',
        'templates/reports'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ تم إنشاء المجلد: {directory}")

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        sys.exit(1)
    print(f"✅ إصدار Python: {sys.version}")

def install_requirements():
    """تثبيت المتطلبات"""
    try:
        import subprocess
        print("📦 جاري تثبيت المتطلبات...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
        else:
            print(f"❌ خطأ في تثبيت المتطلبات: {result.stderr}")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def setup_database():
    """إعداد قاعدة البيانات"""
    try:
        from app import create_app
        from models import db, User
        from werkzeug.security import generate_password_hash
        
        app = create_app()
        with app.app_context():
            db.create_all()
            
            # إنشاء مستخدم افتراضي
            if not User.query.filter_by(username='admin').first():
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    full_name='مدير النظام',
                    role='admin'
                )
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
            else:
                print("ℹ️ المستخدم الافتراضي موجود بالفعل")
                
        print("✅ تم إعداد قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية للإعداد"""
    print("🚗 إعداد نظام إدارة مركز صيانة السيارات")
    print("=" * 60)
    
    # فحص إصدار Python
    check_python_version()
    
    # إنشاء المجلدات
    print("\n📁 إنشاء المجلدات...")
    create_directories()
    
    # تثبيت المتطلبات
    print("\n📦 تثبيت المتطلبات...")
    install_requirements()
    
    # إعداد قاعدة البيانات
    print("\n🗄️ إعداد قاعدة البيانات...")
    setup_database()
    
    print("\n" + "=" * 60)
    print("🎉 تم إعداد النظام بنجاح!")
    print("🌐 لتشغيل النظام: python test_app.py")
    print("🌐 الرابط: http://localhost:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 60)

if __name__ == '__main__':
    main()
