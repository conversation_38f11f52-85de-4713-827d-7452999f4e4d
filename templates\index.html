{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">لوحة التحكم</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-calendar"></i>
                اليوم: {{ moment().format('YYYY-MM-DD') }}
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي العملاء
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.customers_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي المركبات
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.vehicles_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-car-front fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            خدمات معلقة
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.pending_services }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            خدمات قيد التنفيذ
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ stats.in_progress_services }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-tools fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Row -->
<div class="row mb-4">
    {% if stats.low_stock_parts > 0 %}
    <div class="col-md-6">
        <div class="alert alert-warning" role="alert">
            <h4 class="alert-heading">
                <i class="bi bi-exclamation-triangle"></i>
                تنبيه مخزون منخفض
            </h4>
            <p>يوجد {{ stats.low_stock_parts }} قطعة غيار بمخزون منخفض</p>
            <hr>
            <p class="mb-0">
                <a href="{{ url_for('inventory.low_stock') }}" class="btn btn-warning btn-sm">
                    عرض التفاصيل
                </a>
            </p>
        </div>
    </div>
    {% endif %}

    {% if stats.unpaid_invoices > 0 %}
    <div class="col-md-6">
        <div class="alert alert-info" role="alert">
            <h4 class="alert-heading">
                <i class="bi bi-receipt"></i>
                فواتير غير مدفوعة
            </h4>
            <p>يوجد {{ stats.unpaid_invoices }} فاتورة غير مدفوعة</p>
            <hr>
            <p class="mb-0">
                <a href="{{ url_for('reports.financial') }}" class="btn btn-info btn-sm">
                    عرض التقارير المالية
                </a>
            </p>
        </div>
    </div>
    {% endif %}
</div>

<!-- Recent Services and Low Stock -->
<div class="row">
    <!-- Recent Services -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    آخر الخدمات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_services %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الخدمة</th>
                                <th>العميل</th>
                                <th>المركبة</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in recent_services %}
                            <tr>
                                <td>{{ service.service_number }}</td>
                                <td>{{ service.customer.name }}</td>
                                <td>{{ service.vehicle.make }} {{ service.vehicle.model }}</td>
                                <td>
                                    <span class="badge status-badge 
                                        {% if service.status == 'pending' %}bg-warning
                                        {% elif service.status == 'in_progress' %}bg-info
                                        {% elif service.status == 'completed' %}bg-success
                                        {% else %}bg-secondary{% endif %}">
                                        {% if service.status == 'pending' %}معلق
                                        {% elif service.status == 'in_progress' %}قيد التنفيذ
                                        {% elif service.status == 'completed' %}مكتمل
                                        {% else %}{{ service.status }}{% endif %}
                                    </span>
                                </td>
                                <td>{{ service.requested_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('services.view', id=service.id) }}" class="btn btn-sm btn-outline-primary">
                                        عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد خدمات حديثة</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('services.index') }}" class="btn btn-primary btn-sm">
                    عرض جميع الخدمات
                </a>
            </div>
        </div>
    </div>

    <!-- Low Stock Parts -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                    مخزون منخفض
                </h5>
            </div>
            <div class="card-body">
                {% if low_stock_parts %}
                <div class="list-group list-group-flush">
                    {% for part in low_stock_parts %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ part.name }}</h6>
                            <small class="text-muted">{{ part.part_number }}</small>
                        </div>
                        <span class="badge bg-danger rounded-pill">
                            {{ part.quantity_in_stock }}
                        </span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">جميع قطع الغيار متوفرة</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('inventory.low_stock') }}" class="btn btn-warning btn-sm">
                    عرض جميع التنبيهات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('customers.add') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-person-plus"></i>
                            إضافة عميل جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('vehicles.add') }}" class="btn btn-outline-success w-100">
                            <i class="bi bi-car-front-fill"></i>
                            إضافة مركبة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('services.add') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-tools"></i>
                            إضافة خدمة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('inventory.add') }}" class="btn btn-outline-warning w-100">
                            <i class="bi bi-box-seam"></i>
                            إضافة قطعة غيار
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
