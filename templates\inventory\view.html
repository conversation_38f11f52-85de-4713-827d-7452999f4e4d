{% extends "base.html" %}

{% block title %}{{ part.name }} - تفاصيل قطعة الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-box-seam"></i>
        {{ part.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('inventory.edit', id=part.id) }}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i>
                تعديل
            </a>
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#stockModal">
                <i class="bi bi-arrow-up-down"></i>
                تعديل المخزون
            </button>
            <button type="button" class="btn btn-info" onclick="printPart()">
                <i class="bi bi-printer"></i>
                طباعة
            </button>
        </div>
        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للمخزون
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات القطعة -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات قطعة الغيار
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم القطعة:</strong></td>
                                <td>{{ part.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم القطعة:</strong></td>
                                <td><code>{{ part.part_number }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>الفئة:</strong></td>
                                <td>{{ part.category or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>الماركة:</strong></td>
                                <td>{{ part.brand or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>وحدة القياس:</strong></td>
                                <td>{{ part.unit }}</td>
                            </tr>
                            <tr>
                                <td><strong>موقع التخزين:</strong></td>
                                <td>{{ part.location or 'غير محدد' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المخزون الحالي:</strong></td>
                                <td>
                                    <span class="badge {% if part.is_low_stock %}bg-danger{% else %}bg-success{% endif %} fs-6">
                                        {{ part.quantity_in_stock }} {{ part.unit }}
                                    </span>
                                    {% if part.is_low_stock %}
                                    <br><small class="text-danger">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        مخزون منخفض
                                    </small>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحد الأدنى:</strong></td>
                                <td>{{ part.minimum_stock }} {{ part.unit }}</td>
                            </tr>
                            <tr>
                                <td><strong>سعر التكلفة:</strong></td>
                                <td>{{ "%.2f"|format(part.cost_price) }} ر.س</td>
                            </tr>
                            <tr>
                                <td><strong>سعر البيع:</strong></td>
                                <td>{{ "%.2f"|format(part.selling_price) }} ر.س</td>
                            </tr>
                            <tr>
                                <td><strong>هامش الربح:</strong></td>
                                <td>
                                    {% set profit_margin = ((part.selling_price - part.cost_price) / part.cost_price * 100) if part.cost_price > 0 else 0 %}
                                    <span class="{% if profit_margin > 20 %}text-success{% elif profit_margin > 10 %}text-warning{% else %}text-danger{% endif %}">
                                        {{ "%.1f"|format(profit_margin) }}%
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if part.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if part.description %}
                <div class="mt-3">
                    <h6><strong>الوصف:</strong></h6>
                    <p class="text-muted">{{ part.description }}</p>
                </div>
                {% endif %}

                {% if part.notes %}
                <div class="mt-3">
                    <h6><strong>ملاحظات:</strong></h6>
                    <p class="text-muted">{{ part.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- تاريخ حركة المخزون -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    تاريخ حركة المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if movements %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>نوع الحركة</th>
                                <th>الكمية</th>
                                <th>المرجع</th>
                                <th>المستخدم</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in movements %}
                            <tr>
                                <td>{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="badge 
                                        {% if movement.movement_type == 'in' %}bg-success
                                        {% elif movement.movement_type == 'out' %}bg-danger
                                        {% else %}bg-info{% endif %}">
                                        {% if movement.movement_type == 'in' %}إدخال
                                        {% elif movement.movement_type == 'out' %}إخراج
                                        {% else %}تعديل{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if movement.movement_type == 'out' %}-{% endif %}{{ movement.quantity }} {{ part.unit }}
                                </td>
                                <td>{{ movement.reference_number or '-' }}</td>
                                <td>{{ movement.user.full_name if movement.user else '-' }}</td>
                                <td>{{ movement.notes or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-clock-history display-4 text-muted"></i>
                    <h6 class="mt-2">لا توجد حركات مخزون</h6>
                    <p class="text-muted">لم يتم تسجيل أي حركة لهذه القطعة بعد</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الملخص والإحصائيات -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calculator"></i>
                    ملخص مالي
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td>قيمة المخزون:</td>
                        <td class="text-end">
                            <strong>{{ "%.2f"|format(part.quantity_in_stock * part.cost_price) }} ر.س</strong>
                        </td>
                    </tr>
                    <tr>
                        <td>قيمة البيع المتوقعة:</td>
                        <td class="text-end">
                            <strong>{{ "%.2f"|format(part.quantity_in_stock * part.selling_price) }} ر.س</strong>
                        </td>
                    </tr>
                    <tr>
                        <td>الربح المتوقع:</td>
                        <td class="text-end">
                            <strong class="text-success">
                                {{ "%.2f"|format(part.quantity_in_stock * (part.selling_price - part.cost_price)) }} ر.س
                            </strong>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        {% if part.supplier_name %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-truck"></i>
                    معلومات المورد
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>اسم المورد:</strong></td>
                        <td>{{ part.supplier_name }}</td>
                    </tr>
                    {% if part.supplier_contact %}
                    <tr>
                        <td><strong>معلومات الاتصال:</strong></td>
                        <td>
                            <a href="tel:{{ part.supplier_contact }}" class="text-decoration-none">
                                {{ part.supplier_contact }}
                            </a>
                        </td>
                    </tr>
                    {% endif %}
                </table>
                
                {% if part.is_low_stock %}
                <div class="alert alert-warning alert-sm">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> المخزون منخفض، يُنصح بالتواصل مع المورد
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#stockModal">
                        <i class="bi bi-plus-circle"></i>
                        إضافة للمخزون
                    </button>
                    
                    {% if part.quantity_in_stock > 0 %}
                    <button type="button" class="btn btn-warning btn-sm" onclick="showOutModal()">
                        <i class="bi bi-dash-circle"></i>
                        خصم من المخزون
                    </button>
                    {% endif %}
                    
                    <button type="button" class="btn btn-info btn-sm" onclick="showAdjustModal()">
                        <i class="bi bi-arrow-up-down"></i>
                        تعديل الكمية
                    </button>
                    
                    <a href="{{ url_for('inventory.edit', id=part.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-pencil"></i>
                        تعديل البيانات
                    </a>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td>تاريخ الإضافة:</td>
                        <td>{{ part.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td>آخر تحديث:</td>
                        <td>{{ part.updated_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td>عدد الحركات:</td>
                        <td>{{ movements|length }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل المخزون -->
<div class="modal fade" id="stockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('inventory.adjust_stock', id=part.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="movement_type" class="form-label">نوع الحركة</label>
                        <select class="form-select" id="movement_type" name="movement_type" required>
                            <option value="in">إضافة للمخزون</option>
                            <option value="out">خصم من المخزون</option>
                            <option value="adjustment">تعديل الكمية</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">الكمية</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                        <div class="form-text">المخزون الحالي: {{ part.quantity_in_stock }} {{ part.unit }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reference_number" class="form-label">رقم مرجعي</label>
                        <input type="text" class="form-control" id="reference_number" name="reference_number" 
                               placeholder="رقم الفاتورة أو المرجع">
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showOutModal() {
    document.getElementById('movement_type').value = 'out';
    document.getElementById('quantity').max = {{ part.quantity_in_stock }};
    new bootstrap.Modal(document.getElementById('stockModal')).show();
}

function showAdjustModal() {
    document.getElementById('movement_type').value = 'adjustment';
    document.getElementById('quantity').value = {{ part.quantity_in_stock }};
    new bootstrap.Modal(document.getElementById('stockModal')).show();
}

function printPart() {
    window.print();
}

// تحديث حد الكمية حسب نوع الحركة
document.getElementById('movement_type').addEventListener('change', function() {
    const quantityInput = document.getElementById('quantity');
    if (this.value === 'out') {
        quantityInput.max = {{ part.quantity_in_stock }};
        quantityInput.placeholder = 'الحد الأقصى: {{ part.quantity_in_stock }}';
    } else {
        quantityInput.removeAttribute('max');
        quantityInput.placeholder = '';
    }
});
</script>
{% endblock %}
