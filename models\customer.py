from . import db
from datetime import datetime

class Customer(db.Model):
    """نموذج العملاء"""
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    national_id = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    vehicles = db.relationship('Vehicle', backref='customer', lazy=True, cascade='all, delete-orphan')
    services = db.relationship('Service', backref='customer', lazy=True)
    
    def __repr__(self):
        return f'<Customer {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'national_id': self.national_id,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'vehicles_count': len(self.vehicles),
            'services_count': len(self.services)
        }
