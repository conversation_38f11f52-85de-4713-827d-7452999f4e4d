#!/usr/bin/env python3
"""
ملف تشغيل نهائي لنظام إدارة مركز صيانة السيارات
"""

import sys
import webbrowser
import time
import threading

def open_browser_delayed(url, delay=3):
    """فتح المتصفح بعد تأخير"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
        except:
            pass

    thread = threading.Thread(target=open_browser, daemon=True)
    thread.start()

print("🚗 نظام إدارة مركز صيانة السيارات")
print("=" * 50)

try:
    # استيراد التطبيق
    print("📦 جاري تحميل التطبيق...")
    try:
        from app_complete import app
        print("✅ تم تحميل التطبيق الكامل بنجاح")
    except ImportError:
        print("⚠️ التطبيق الكامل غير متاح، جاري تحميل النسخة البسيطة...")
        from test_app import app
        print("✅ تم تحميل التطبيق البسيط بنجاح")

    # معلومات التشغيل
    url = "http://localhost:5000"
    print("\n" + "=" * 50)
    print("🚀 تم تشغيل النظام بنجاح!")
    print(f"🌐 الرابط: {url}")
    print("👤 المستخدم الافتراضي: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 50)
    print("💡 اضغط Ctrl+C لإيقاف النظام")
    print("=" * 50)

    # فتح المتصفح تلقائياً
    open_browser_delayed(url)

    # تشغيل التطبيق
    app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)

except KeyboardInterrupt:
    print("\n👋 تم إيقاف النظام بنجاح")
except Exception as e:
    print(f"\n❌ خطأ في تشغيل النظام: {e}")
    print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
    sys.exit(1)
