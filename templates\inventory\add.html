{% extends "base.html" %}

{% block title %}إضافة قطعة غيار جديدة - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-box-seam"></i>
        إضافة قطعة غيار جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للمخزون
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">بيانات قطعة الغيار</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="partForm">
                    <!-- معلومات أساسية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم القطعة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   placeholder="مثل: فلتر زيت، بطارية، إطار" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="part_number" class="form-label">رقم القطعة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="part_number" name="part_number" 
                                   placeholder="رقم فريد للقطعة" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="2" 
                                  placeholder="وصف تفصيلي للقطعة..."></textarea>
                    </div>

                    <!-- تصنيف وماركة -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">اختر الفئة</option>
                                <option value="محرك">محرك</option>
                                <option value="فرامل">فرامل</option>
                                <option value="إطارات">إطارات</option>
                                <option value="كهرباء">كهرباء</option>
                                <option value="تكييف">تكييف</option>
                                <option value="زيوت">زيوت</option>
                                <option value="فلاتر">فلاتر</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="brand" class="form-label">الماركة</label>
                            <input type="text" class="form-control" id="brand" name="brand" 
                                   placeholder="مثل: بوش، دينسو، فالفولين">
                        </div>
                    </div>

                    <!-- معلومات المخزون -->
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="quantity_in_stock" class="form-label">الكمية الحالية</label>
                            <input type="number" class="form-control" id="quantity_in_stock" name="quantity_in_stock" 
                                   min="0" value="0">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="minimum_stock" class="form-label">الحد الأدنى</label>
                            <input type="number" class="form-control" id="minimum_stock" name="minimum_stock" 
                                   min="1" value="5">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="unit" class="form-label">وحدة القياس</label>
                            <select class="form-select" id="unit" name="unit">
                                <option value="قطعة">قطعة</option>
                                <option value="لتر">لتر</option>
                                <option value="كيلو">كيلو</option>
                                <option value="متر">متر</option>
                                <option value="علبة">علبة</option>
                                <option value="زجاجة">زجاجة</option>
                            </select>
                        </div>
                    </div>

                    <!-- الأسعار -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cost_price" class="form-label">سعر التكلفة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                       step="0.01" min="0" required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="selling_price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                       step="0.01" min="0" required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات المورد -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supplier_name" class="form-label">اسم المورد</label>
                            <input type="text" class="form-control" id="supplier_name" name="supplier_name" 
                                   placeholder="اسم الشركة أو المورد">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="supplier_contact" class="form-label">معلومات الاتصال</label>
                            <input type="text" class="form-control" id="supplier_contact" name="supplier_contact" 
                                   placeholder="رقم الهاتف أو البريد الإلكتروني">
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="location" class="form-label">موقع التخزين</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   placeholder="مثل: رف A1، مخزن 2">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الربح المتوقع</label>
                            <div class="form-control-plaintext" id="profit_margin">
                                <span class="text-muted">سيتم حسابه تلقائياً</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي معلومات إضافية..."></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            حفظ قطعة الغيار
                        </button>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-lg"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        رقم القطعة يجب أن يكون فريد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        سعر البيع يجب أن يكون أعلى من التكلفة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        الحد الأدنى للتنبيه عند نفاد المخزون
                    </li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calculator"></i>
                    حاسبة الربح
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-2">
                        <small class="text-muted">هامش الربح</small>
                        <div class="h4 text-success" id="profit_percentage">0%</div>
                    </div>
                    <div class="col-12">
                        <small class="text-muted">الربح لكل قطعة</small>
                        <div class="h5 text-primary" id="profit_amount">0.00 ر.س</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// حساب الربح تلقائياً
function calculateProfit() {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (costPrice > 0 && sellingPrice > 0) {
        const profit = sellingPrice - costPrice;
        const profitPercentage = ((profit / costPrice) * 100).toFixed(1);
        
        document.getElementById('profit_amount').textContent = profit.toFixed(2) + ' ر.س';
        document.getElementById('profit_percentage').textContent = profitPercentage + '%';
        
        // تحديث لون النص حسب الربح
        const profitElement = document.getElementById('profit_percentage');
        if (profit > 0) {
            profitElement.className = 'h4 text-success';
        } else if (profit < 0) {
            profitElement.className = 'h4 text-danger';
        } else {
            profitElement.className = 'h4 text-warning';
        }
    } else {
        document.getElementById('profit_amount').textContent = '0.00 ر.س';
        document.getElementById('profit_percentage').textContent = '0%';
    }
}

// ربط الأحداث
document.getElementById('cost_price').addEventListener('input', calculateProfit);
document.getElementById('selling_price').addEventListener('input', calculateProfit);

// التحقق من صحة النموذج
document.getElementById('partForm').addEventListener('submit', function(e) {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (sellingPrice <= costPrice) {
        e.preventDefault();
        alert('سعر البيع يجب أن يكون أعلى من سعر التكلفة');
        return false;
    }
});
</script>
{% endblock %}
