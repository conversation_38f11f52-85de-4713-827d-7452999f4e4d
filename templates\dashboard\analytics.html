{% extends "base.html" %}

{% block title %}لوحة التحليلات المتقدمة - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-graph-up-arrow"></i>
        لوحة التحليلات المتقدمة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise"></i>
                تحديث البيانات
            </button>
            <button type="button" class="btn btn-outline-success" onclick="exportAnalytics()">
                <i class="bi bi-download"></i>
                تصدير التحليلات
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-calendar-range"></i>
                الفترة الزمنية
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?period=today">اليوم</a></li>
                <li><a class="dropdown-item" href="?period=week">هذا الأسبوع</a></li>
                <li><a class="dropdown-item" href="?period=month">هذا الشهر</a></li>
                <li><a class="dropdown-item" href="?period=quarter">هذا الربع</a></li>
                <li><a class="dropdown-item" href="?period=year">هذا العام</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- مؤشرات الأداء الرئيسية -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            معدل النمو الشهري
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            +{{ "%.1f"|format(growth_rate) }}%
                        </div>
                        <div class="small text-muted">
                            مقارنة بالشهر الماضي
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-trending-up fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            متوسط قيمة الخدمة
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ "%.0f"|format(avg_service_value) }} ر.س
                        </div>
                        <div class="small text-muted">
                            آخر 30 يوم
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            معدل رضا العملاء
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ "%.1f"|format(customer_satisfaction) }}%
                        </div>
                        <div class="small text-muted">
                            بناءً على التقييمات
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-star-fill fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            كفاءة الفنيين
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ "%.1f"|format(technician_efficiency) }}%
                        </div>
                        <div class="small text-muted">
                            متوسط الإنجاز في الوقت
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-gear fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية المتقدمة -->
<div class="row">
    <!-- تحليل الإيرادات -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    تحليل الإيرادات والأرباح
                </h5>
            </div>
            <div class="card-body">
                <canvas id="revenueAnalysisChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- توزيع أنواع الخدمات -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    توزيع أنواع الخدمات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="serviceTypesChart" width="300" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- تحليلات متقدمة -->
<div class="row mt-4">
    <!-- أداء الفنيين -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people"></i>
                    تحليل أداء الفنيين
                </h5>
            </div>
            <div class="card-body">
                <canvas id="technicianPerformanceChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- تحليل العملاء -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-hearts"></i>
                    تحليل سلوك العملاء
                </h5>
            </div>
            <div class="card-body">
                <canvas id="customerBehaviorChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- جداول التحليل التفصيلي -->
<div class="row mt-4">
    <!-- أفضل العملاء -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-trophy"></i>
                    أفضل العملاء
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>عدد الخدمات</th>
                                <th>إجمالي الإنفاق</th>
                                <th>آخر زيارة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in top_customers %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <span class="badge bg-primary rounded-circle">{{ customer.name[0] }}</span>
                                        </div>
                                        <div>
                                            <strong>{{ customer.name }}</strong>
                                            <br><small class="text-muted">{{ customer.phone }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ customer.services_count }}</span>
                                </td>
                                <td>
                                    <strong>{{ "%.0f"|format(customer.total_spent) }} ر.س</strong>
                                </td>
                                <td>
                                    <small>{{ customer.last_visit.strftime('%Y-%m-%d') if customer.last_visit else 'لا يوجد' }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- أكثر قطع الغيار استخداماً -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-box-seam"></i>
                    أكثر قطع الغيار استخداماً
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>قطعة الغيار</th>
                                <th>مرات الاستخدام</th>
                                <th>الإيرادات</th>
                                <th>الربح</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for part in top_parts %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ part.name }}</strong>
                                        <br><small class="text-muted">{{ part.part_number }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ part.usage_count }}</span>
                                </td>
                                <td>
                                    <strong>{{ "%.0f"|format(part.total_revenue) }} ر.س</strong>
                                </td>
                                <td>
                                    <span class="text-success">
                                        <strong>{{ "%.0f"|format(part.total_profit) }} ر.س</strong>
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحليل الاتجاهات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-down-arrow"></i>
                    تحليل الاتجاهات والتوقعات
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <canvas id="trendsChart" width="600" height="300"></canvas>
                    </div>
                    <div class="col-lg-4">
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">نمو الإيرادات</h6>
                                    <small class="text-success">+15.2%</small>
                                </div>
                                <p class="mb-1">زيادة مستمرة في الإيرادات الشهرية</p>
                                <small>مقارنة بالفترة السابقة</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">رضا العملاء</h6>
                                    <small class="text-info">+8.7%</small>
                                </div>
                                <p class="mb-1">تحسن في معدل رضا العملاء</p>
                                <small>بناءً على التقييمات الأخيرة</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">كفاءة العمليات</h6>
                                    <small class="text-warning">+5.3%</small>
                                </div>
                                <p class="mb-1">تحسن في أوقات إنجاز الخدمات</p>
                                <small>متوسط الوقت انخفض بـ 12%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تنبيهات ذكية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bell"></i>
                    التنبيهات الذكية والتوصيات
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb"></i>
                            <strong>توصية:</strong> زيادة مخزون زيت المحرك بنسبة 20% لتلبية الطلب المتزايد
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>تنبيه:</strong> انخفاض في معدل العملاء الجدد هذا الشهر بنسبة 8%
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            <strong>إنجاز:</strong> تحقيق هدف الإيرادات الشهرية بنسبة 105%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// بيانات وهمية للتحليلات (في التطبيق الحقيقي ستأتي من API)
const analyticsData = {
    revenue: [120000, 135000, 142000, 158000, 165000, 172000],
    profit: [45000, 52000, 58000, 65000, 68000, 72000],
    months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
    serviceTypes: {
        labels: ['صيانة دورية', 'إصلاح عطل', 'تغيير زيت', 'فحص شامل', 'أخرى'],
        data: [35, 25, 20, 15, 5]
    },
    technicianPerformance: {
        labels: ['أحمد محمد', 'سعد العتيبي', 'محمد الأحمد', 'عبدالله السعد'],
        efficiency: [95, 88, 92, 85],
        completed: [45, 38, 42, 35]
    }
};

// رسم تحليل الإيرادات
const revenueCtx = document.getElementById('revenueAnalysisChart').getContext('2d');
new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: analyticsData.months,
        datasets: [{
            label: 'الإيرادات',
            data: analyticsData.revenue,
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'الأرباح',
            data: analyticsData.profit,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'تطور الإيرادات والأرباح'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ر.س';
                    }
                }
            }
        }
    }
});

// رسم توزيع أنواع الخدمات
const serviceTypesCtx = document.getElementById('serviceTypesChart').getContext('2d');
new Chart(serviceTypesCtx, {
    type: 'doughnut',
    data: {
        labels: analyticsData.serviceTypes.labels,
        datasets: [{
            data: analyticsData.serviceTypes.data,
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم أداء الفنيين
const techCtx = document.getElementById('technicianPerformanceChart').getContext('2d');
new Chart(techCtx, {
    type: 'bar',
    data: {
        labels: analyticsData.technicianPerformance.labels,
        datasets: [{
            label: 'كفاءة الأداء (%)',
            data: analyticsData.technicianPerformance.efficiency,
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            yAxisID: 'y'
        }, {
            label: 'الخدمات المكتملة',
            data: analyticsData.technicianPerformance.completed,
            backgroundColor: 'rgba(75, 192, 192, 0.8)',
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                max: 100
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// رسم سلوك العملاء
const customerCtx = document.getElementById('customerBehaviorChart').getContext('2d');
new Chart(customerCtx, {
    type: 'radar',
    data: {
        labels: ['الولاء', 'التكرار', 'القيمة', 'الرضا', 'التوصية'],
        datasets: [{
            label: 'العملاء الحاليين',
            data: [85, 78, 92, 88, 82],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)'
        }]
    },
    options: {
        responsive: true,
        scales: {
            r: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// رسم الاتجاهات
const trendsCtx = document.getElementById('trendsChart').getContext('2d');
new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو (توقع)', 'أغسطس (توقع)'],
        datasets: [{
            label: 'الإيرادات الفعلية',
            data: [120000, 135000, 142000, 158000, 165000, 172000, null, null],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)'
        }, {
            label: 'التوقعات',
            data: [null, null, null, null, null, 172000, 185000, 195000],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            borderDash: [5, 5]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'الاتجاهات والتوقعات المستقبلية'
            }
        }
    }
});

// وظائف التفاعل
function refreshData() {
    // تحديث البيانات
    location.reload();
}

function exportAnalytics() {
    // تصدير التحليلات
    alert('سيتم تصدير التحليلات قريباً');
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    // تحديث البيانات عبر AJAX
    console.log('تحديث البيانات...');
}, 300000);
</script>
{% endblock %}
