from . import db
from datetime import datetime

class Invoice(db.Model):
    """نموذج الفواتير"""
    __tablename__ = 'invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    service_id = db.Column(db.Integer, db.<PERSON>Key('services.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    
    # معلومات الفاتورة
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    invoice_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    
    # المبالغ
    subtotal = db.Column(db.Float, default=0.0)  # المجموع الفرعي
    tax_rate = db.Column(db.Float, default=0.15)  # معدل الضريبة
    tax_amount = db.Column(db.Float, default=0.0)  # مبلغ الضريبة
    discount_amount = db.Column(db.Float, default=0.0)  # مبلغ الخصم
    total_amount = db.Column(db.Float, default=0.0)  # المبلغ الإجمالي
    
    # حالة الدفع
    payment_status = db.Column(db.String(20), default='pending')  # pending, partial, paid, overdue
    paid_amount = db.Column(db.Float, default=0.0)  # المبلغ المدفوع
    remaining_amount = db.Column(db.Float, default=0.0)  # المبلغ المتبقي
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    terms_conditions = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    service = db.relationship('Service', backref='invoice', uselist=False)
    customer = db.relationship('Customer', backref='invoices')
    payments = db.relationship('Payment', backref='invoice', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'
    
    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        if self.service:
            self.subtotal = self.service.total_cost
            self.tax_amount = self.subtotal * self.tax_rate
            self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
            self.remaining_amount = self.total_amount - self.paid_amount
            
            # تحديث حالة الدفع
            if self.paid_amount == 0:
                self.payment_status = 'pending'
            elif self.paid_amount >= self.total_amount:
                self.payment_status = 'paid'
                self.remaining_amount = 0
            else:
                self.payment_status = 'partial'
    
    def to_dict(self):
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'service_id': self.service_id,
            'customer_id': self.customer_id,
            'invoice_date': self.invoice_date.isoformat() if self.invoice_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'subtotal': self.subtotal,
            'tax_amount': self.tax_amount,
            'discount_amount': self.discount_amount,
            'total_amount': self.total_amount,
            'payment_status': self.payment_status,
            'paid_amount': self.paid_amount,
            'remaining_amount': self.remaining_amount
        }


class Payment(db.Model):
    """نموذج المدفوعات"""
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    
    # تفاصيل الدفع
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, card, transfer, check
    reference_number = db.Column(db.String(50))  # رقم مرجعي للدفع
    notes = db.Column(db.Text)
    
    # معلومات المستخدم
    received_by = db.Column(db.String(100))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Payment {self.amount} - {self.payment_method}>'
