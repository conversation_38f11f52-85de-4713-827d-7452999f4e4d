{% extends "base.html" %}

{% block title %}مركز الإشعارات - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-bell"></i>
        مركز الإشعارات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="markAllAsRead()">
                <i class="bi bi-check-all"></i>
                تحديد الكل كمقروء
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="clearAllNotifications()">
                <i class="bi bi-trash"></i>
                مسح الكل
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-funnel"></i>
                فلترة
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?filter=all">جميع الإشعارات</a></li>
                <li><a class="dropdown-item" href="?filter=unread">غير مقروءة</a></li>
                <li><a class="dropdown-item" href="?filter=urgent">عاجلة</a></li>
                <li><a class="dropdown-item" href="?filter=system">النظام</a></li>
                <li><a class="dropdown-item" href="?filter=services">الخدمات</a></li>
                <li><a class="dropdown-item" href="?filter=inventory">المخزون</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- إحصائيات الإشعارات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card info">
            <div class="card-body text-center">
                <h3 class="text-white">{{ total_notifications }}</h3>
                <p class="mb-0">إجمالي الإشعارات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card warning">
            <div class="card-body text-center">
                <h3 class="text-white">{{ unread_notifications }}</h3>
                <p class="mb-0">غير مقروءة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card danger">
            <div class="card-body text-center">
                <h3 class="text-white">{{ urgent_notifications }}</h3>
                <p class="mb-0">عاجلة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card success">
            <div class="card-body text-center">
                <h3 class="text-white">{{ today_notifications }}</h3>
                <p class="mb-0">اليوم</p>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الإشعارات -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i>
                    الإشعارات الحديثة
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="notifications-list">
                    <!-- إشعار مخزون منخفض -->
                    <div class="notification-item unread urgent" data-id="1">
                        <div class="notification-icon bg-danger">
                            <i class="bi bi-exclamation-triangle text-white"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title">تنبيه مخزون منخفض</h6>
                                <span class="notification-time">منذ 5 دقائق</span>
                            </div>
                            <p class="notification-message">
                                قطعة "زيت المحرك 5W-30" وصلت للحد الأدنى (5 قطع متبقية)
                            </p>
                            <div class="notification-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewInventory()">
                                    عرض المخزون
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="orderPart(1)">
                                    طلب قطع غيار
                                </button>
                            </div>
                        </div>
                        <div class="notification-controls">
                            <button class="btn btn-sm btn-outline-secondary" onclick="markAsRead(1)">
                                <i class="bi bi-check"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(1)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>

                    <!-- إشعار خدمة جديدة -->
                    <div class="notification-item unread" data-id="2">
                        <div class="notification-icon bg-info">
                            <i class="bi bi-tools text-white"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title">خدمة جديدة</h6>
                                <span class="notification-time">منذ 15 دقيقة</span>
                            </div>
                            <p class="notification-message">
                                تم إضافة خدمة جديدة للعميل "أحمد محمد" - مركبة "تويوتا كامري"
                            </p>
                            <div class="notification-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewService(123)">
                                    عرض الخدمة
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="assignTechnician(123)">
                                    تخصيص فني
                                </button>
                            </div>
                        </div>
                        <div class="notification-controls">
                            <button class="btn btn-sm btn-outline-secondary" onclick="markAsRead(2)">
                                <i class="bi bi-check"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(2)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>

                    <!-- إشعار خدمة مكتملة -->
                    <div class="notification-item read" data-id="3">
                        <div class="notification-icon bg-success">
                            <i class="bi bi-check-circle text-white"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title">خدمة مكتملة</h6>
                                <span class="notification-time">منذ ساعة</span>
                            </div>
                            <p class="notification-message">
                                تم إكمال خدمة "تغيير زيت" للعميل "سعد العتيبي" بواسطة الفني "محمد الأحمد"
                            </p>
                            <div class="notification-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewService(122)">
                                    عرض الخدمة
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="createInvoice(122)">
                                    إنشاء فاتورة
                                </button>
                            </div>
                        </div>
                        <div class="notification-controls">
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(3)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>

                    <!-- إشعار دفعة جديدة -->
                    <div class="notification-item read" data-id="4">
                        <div class="notification-icon bg-warning">
                            <i class="bi bi-currency-dollar text-white"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title">دفعة جديدة</h6>
                                <span class="notification-time">منذ ساعتين</span>
                            </div>
                            <p class="notification-message">
                                تم استلام دفعة بقيمة 450 ر.س من العميل "عبدالله السعد" للفاتورة #INV-2024-001
                            </p>
                            <div class="notification-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewInvoice('INV-2024-001')">
                                    عرض الفاتورة
                                </button>
                            </div>
                        </div>
                        <div class="notification-controls">
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(4)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>

                    <!-- إشعار نظام -->
                    <div class="notification-item read" data-id="5">
                        <div class="notification-icon bg-secondary">
                            <i class="bi bi-gear text-white"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title">تحديث النظام</h6>
                                <span class="notification-time">منذ 3 ساعات</span>
                            </div>
                            <p class="notification-message">
                                تم تحديث النظام بنجاح إلى الإصدار 1.2.0 - تحسينات في الأداء وإصلاح أخطاء
                            </p>
                            <div class="notification-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewChangelog()">
                                    عرض التغييرات
                                </button>
                            </div>
                        </div>
                        <div class="notification-controls">
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(5)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تحميل المزيد -->
                <div class="text-center p-3">
                    <button class="btn btn-outline-primary" onclick="loadMoreNotifications()">
                        <i class="bi bi-arrow-down"></i>
                        تحميل المزيد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات الإشعارات -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    إعدادات الإشعارات
                </h5>
            </div>
            <div class="card-body">
                <form id="notificationSettings">
                    <div class="mb-3">
                        <label class="form-label">إشعارات المخزون</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="lowStock" checked>
                            <label class="form-check-label" for="lowStock">
                                تنبيه المخزون المنخفض
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="outOfStock" checked>
                            <label class="form-check-label" for="outOfStock">
                                تنبيه نفاد المخزون
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">إشعارات الخدمات</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="newService" checked>
                            <label class="form-check-label" for="newService">
                                خدمات جديدة
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="completedService" checked>
                            <label class="form-check-label" for="completedService">
                                خدمات مكتملة
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="overdueService">
                            <label class="form-check-label" for="overdueService">
                                خدمات متأخرة
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">إشعارات مالية</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="newPayment" checked>
                            <label class="form-check-label" for="newPayment">
                                دفعات جديدة
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="overdueInvoice" checked>
                            <label class="form-check-label" for="overdueInvoice">
                                فواتير متأخرة
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">طريقة الإشعار</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="browserNotification" checked>
                            <label class="form-check-label" for="browserNotification">
                                إشعارات المتصفح
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="emailNotification">
                            <label class="form-check-label" for="emailNotification">
                                إشعارات البريد الإلكتروني
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="smsNotification">
                            <label class="form-check-label" for="smsNotification">
                                إشعارات الرسائل النصية
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check"></i>
                        حفظ الإعدادات
                    </button>
                </form>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary">15</h4>
                        <small class="text-muted">خدمات اليوم</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">8</h4>
                        <small class="text-muted">خدمات مكتملة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">3</h4>
                        <small class="text-muted">تنبيهات مخزون</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">12,500</h4>
                        <small class="text-muted">إيرادات اليوم</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notification-item.urgent {
    border-left-color: #f44336;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-title {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.notification-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-right: auto;
}

.notification-message {
    font-size: 0.85rem;
    color: #495057;
    margin-bottom: 0.5rem;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.notification-controls {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-right: 0.5rem;
}

.stats-card {
    border: none;
    border-radius: 10px;
    color: white;
}

.stats-card.info { background: linear-gradient(45deg, #17a2b8, #20c997); }
.stats-card.warning { background: linear-gradient(45deg, #ffc107, #fd7e14); }
.stats-card.danger { background: linear-gradient(45deg, #dc3545, #e83e8c); }
.stats-card.success { background: linear-gradient(45deg, #28a745, #20c997); }
</style>
{% endblock %}

{% block extra_js %}
<script>
// وظائف الإشعارات
function markAsRead(notificationId) {
    const notification = document.querySelector(`[data-id="${notificationId}"]`);
    notification.classList.remove('unread');
    
    // إرسال طلب AJAX لتحديث الحالة
    fetch(`/notifications/${notificationId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    });
}

function deleteNotification(notificationId) {
    if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        const notification = document.querySelector(`[data-id="${notificationId}"]`);
        notification.remove();
        
        // إرسال طلب AJAX للحذف
        fetch(`/notifications/${notificationId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });
    }
}

function markAllAsRead() {
    document.querySelectorAll('.notification-item.unread').forEach(item => {
        item.classList.remove('unread');
    });
    
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    });
}

function clearAllNotifications() {
    if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
        document.querySelector('.notifications-list').innerHTML = 
            '<div class="text-center p-4"><p class="text-muted">لا توجد إشعارات</p></div>';
        
        fetch('/notifications/clear-all', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });
    }
}

function loadMoreNotifications() {
    // تحميل المزيد من الإشعارات
    console.log('تحميل المزيد من الإشعارات...');
}

// وظائف الإجراءات
function viewInventory() {
    window.location.href = '/inventory';
}

function orderPart(partId) {
    window.location.href = `/inventory/${partId}/order`;
}

function viewService(serviceId) {
    window.location.href = `/services/${serviceId}`;
}

function assignTechnician(serviceId) {
    window.location.href = `/services/${serviceId}/assign`;
}

function createInvoice(serviceId) {
    window.location.href = `/services/${serviceId}/invoice`;
}

function viewInvoice(invoiceNumber) {
    window.location.href = `/invoices/${invoiceNumber}`;
}

function viewChangelog() {
    window.open('/changelog', '_blank');
}

// حفظ إعدادات الإشعارات
document.getElementById('notificationSettings').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const settings = {};
    
    // جمع الإعدادات
    document.querySelectorAll('#notificationSettings input[type="checkbox"]').forEach(checkbox => {
        settings[checkbox.id] = checkbox.checked;
    });
    
    // حفظ الإعدادات
    fetch('/notifications/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ الإعدادات بنجاح');
        }
    });
});

// طلب إذن الإشعارات من المتصفح
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}

// تحديث الإشعارات كل دقيقة
setInterval(function() {
    fetch('/api/notifications/latest')
        .then(response => response.json())
        .then(data => {
            // تحديث الإشعارات الجديدة
            if (data.new_notifications > 0) {
                // إظهار إشعار المتصفح
                if (Notification.permission === 'granted') {
                    new Notification('إشعارات جديدة', {
                        body: `لديك ${data.new_notifications} إشعار جديد`,
                        icon: '/static/images/logo.png'
                    });
                }
            }
        });
}, 60000);
</script>
{% endblock %}
