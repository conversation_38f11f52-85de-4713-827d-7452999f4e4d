#!/usr/bin/env python3
"""
ملف تشغيل سريع لنظام إدارة مركز صيانة السيارات
"""

import os
import sys
import webbrowser
import time
from pathlib import Path

def check_requirements():
    """فحص المتطلبات"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ متطلب مفقود: {e}")
        print("💡 قم بتشغيل: pip install -r requirements.txt")
        return False

def create_basic_directories():
    """إنشاء المجلدات الأساسية"""
    directories = ['instance', 'static/uploads']
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def main():
    """الدالة الرئيسية"""
    print("🚗 نظام إدارة مركز صيانة السيارات")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # إنشاء المجلدات الأساسية
    create_basic_directories()
    
    # تشغيل التطبيق
    try:
        print("🚀 جاري تشغيل النظام...")
        
        # استيراد وتشغيل التطبيق
        from test_app import app
        
        print("✅ تم تشغيل النظام بنجاح!")
        print("🌐 الرابط: http://localhost:5000")
        print("👤 المستخدم الافتراضي: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 50)
        print("💡 اضغط Ctrl+C لإيقاف النظام")
        
        # فتح المتصفح تلقائياً بعد ثانيتين
        import threading
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:5000')
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        # تشغيل التطبيق
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")

if __name__ == '__main__':
    main()
