{% extends "base.html" %}

{% block title %}تنبيهات المخزون المنخفض - نظام إدارة مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-exclamation-triangle text-warning"></i>
        تنبيهات المخزون المنخفض
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('inventory.add') }}" class="btn btn-success">
                <i class="bi bi-plus-circle"></i>
                إضافة قطعة غيار
            </a>
            <button type="button" class="btn btn-info" onclick="printReport()">
                <i class="bi bi-printer"></i>
                طباعة التقرير
            </button>
        </div>
        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للمخزون
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card danger">
            <div class="card-body text-center">
                <h3 class="text-white">{{ parts|length }}</h3>
                <p class="mb-0">قطعة منخفضة المخزون</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card warning">
            <div class="card-body text-center">
                <h3 class="text-white">{{ parts|selectattr('quantity_in_stock', 'equalto', 0)|list|length }}</h3>
                <p class="mb-0">قطعة نفدت تماماً</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card info">
            <div class="card-body text-center">
                <h3 class="text-white">{{ parts|map(attribute='quantity_in_stock')|sum }}</h3>
                <p class="mb-0">إجمالي الكمية المتبقية</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card success">
            <div class="card-body text-center">
                <h3 class="text-white">{{ "%.0f"|format(parts|map(attribute='cost_price')|sum) }}</h3>
                <p class="mb-0">قيمة المخزون المنخفض (ر.س)</p>
            </div>
        </div>
    </div>
</div>

{% if parts %}
<!-- جدول قطع الغيار منخفضة المخزون -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-list-ul"></i>
            قطع الغيار التي تحتاج إعادة تموين
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الأولوية</th>
                        <th>القطعة</th>
                        <th>الفئة</th>
                        <th>المخزون الحالي</th>
                        <th>الحد الأدنى</th>
                        <th>الكمية المطلوبة</th>
                        <th>التكلفة المقدرة</th>
                        <th>المورد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for part in parts %}
                    <tr class="{% if part.quantity_in_stock == 0 %}table-danger{% else %}table-warning{% endif %}">
                        <td>
                            {% if part.quantity_in_stock == 0 %}
                                <span class="badge bg-danger">نفدت</span>
                            {% elif part.quantity_in_stock <= part.minimum_stock / 2 %}
                                <span class="badge bg-warning">عاجل</span>
                            {% else %}
                                <span class="badge bg-info">منخفض</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <strong>{{ part.name }}</strong>
                                <br><small class="text-muted">{{ part.part_number }}</small>
                                {% if part.brand %}
                                <br><small class="text-muted">{{ part.brand }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ part.category or '-' }}</td>
                        <td>
                            <span class="badge {% if part.quantity_in_stock == 0 %}bg-danger{% else %}bg-warning{% endif %}">
                                {{ part.quantity_in_stock }} {{ part.unit }}
                            </span>
                        </td>
                        <td>{{ part.minimum_stock }} {{ part.unit }}</td>
                        <td>
                            {% set needed = part.minimum_stock * 2 - part.quantity_in_stock %}
                            <strong class="text-primary">{{ needed }} {{ part.unit }}</strong>
                        </td>
                        <td>
                            {% set needed = part.minimum_stock * 2 - part.quantity_in_stock %}
                            {{ "%.2f"|format(needed * part.cost_price) }} ر.س
                        </td>
                        <td>
                            {% if part.supplier_name %}
                                {{ part.supplier_name }}
                                {% if part.supplier_contact %}
                                <br><small class="text-muted">
                                    <a href="tel:{{ part.supplier_contact }}" class="text-decoration-none">
                                        {{ part.supplier_contact }}
                                    </a>
                                </small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="showRestockModal({{ part.id }}, '{{ part.name }}', {{ part.minimum_stock * 2 - part.quantity_in_stock }})" 
                                        title="إعادة تموين">
                                    <i class="bi bi-plus-circle"></i>
                                </button>
                                <a href="{{ url_for('inventory.view', id=part.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('inventory.edit', id=part.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-active">
                        <th colspan="6">الإجمالي المطلوب:</th>
                        <th>
                            {% set total_cost = 0 %}
                            {% for part in parts %}
                                {% set needed = part.minimum_stock * 2 - part.quantity_in_stock %}
                                {% set total_cost = total_cost + (needed * part.cost_price) %}
                            {% endfor %}
                            {{ "%.2f"|format(total_cost) }} ر.س
                        </th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<!-- تحليل المخزون -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    توزيع المخزون المنخفض حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    أولويات الشراء
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for part in parts[:5] %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ part.name }}</h6>
                            <small class="text-muted">{{ part.part_number }}</small>
                        </div>
                        <div class="text-end">
                            {% set needed = part.minimum_stock * 2 - part.quantity_in_stock %}
                            <span class="badge bg-primary">{{ needed }} {{ part.unit }}</span>
                            <br><small class="text-muted">{{ "%.2f"|format(needed * part.cost_price) }} ر.س</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- لا توجد تنبيهات -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="bi bi-check-circle display-1 text-success"></i>
        <h3 class="mt-3 text-success">ممتاز! لا توجد تنبيهات مخزون</h3>
        <p class="text-muted">جميع قطع الغيار متوفرة بكميات كافية</p>
        <a href="{{ url_for('inventory.index') }}" class="btn btn-primary">
            <i class="bi bi-box-seam"></i>
            عرض المخزون الكامل
        </a>
    </div>
</div>
{% endif %}

<!-- Modal إعادة التموين -->
<div class="modal fade" id="restockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تموين المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="restockForm" method="POST">
                <div class="modal-body">
                    <p>إعادة تموين: <strong id="partName"></strong></p>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">الكمية المضافة</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                        <div class="form-text">الكمية المقترحة: <span id="suggestedQuantity"></span></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reference_number" class="form-label">رقم فاتورة الشراء</label>
                        <input type="text" class="form-control" id="reference_number" name="reference_number" 
                               placeholder="رقم الفاتورة أو المرجع">
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" 
                                  placeholder="ملاحظات حول عملية الشراء..."></textarea>
                    </div>
                    
                    <input type="hidden" name="movement_type" value="in">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة للمخزون</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم توزيع المخزون حسب الفئة
{% if parts %}
const categoryData = {};
{% for part in parts %}
const category = '{{ part.category or "أخرى" }}';
if (!categoryData[category]) {
    categoryData[category] = 0;
}
categoryData[category]++;
{% endfor %}

const ctx = document.getElementById('categoryChart').getContext('2d');
new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: Object.keys(categoryData),
        datasets: [{
            data: Object.values(categoryData),
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// إظهار مودال إعادة التموين
function showRestockModal(partId, partName, suggestedQty) {
    document.getElementById('partName').textContent = partName;
    document.getElementById('suggestedQuantity').textContent = suggestedQty;
    document.getElementById('quantity').value = suggestedQty;
    document.getElementById('restockForm').action = `/inventory/${partId}/adjust_stock`;
    new bootstrap.Modal(document.getElementById('restockModal')).show();
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
